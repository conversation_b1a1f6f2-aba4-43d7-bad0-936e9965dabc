package com.subfg.domain.vo;

import lombok.Data;
import java.util.Map;

/**
 * 用户消息响应VO
 */
@Data
public class UserMessageVo {

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型（1-系统通知 2-业务通知）
     */
    private Integer messageType;

    /**
     * 消息类型描述
     */
    private String messageTypeDesc;

    /**
     * 已读状态（0-未读 1-已读）
     */
    private Integer readStatus;

    /**
     * 已读状态描述
     */
    private String readStatusDesc;

    /**
     * 阅读时间
     */
    private Long readTime;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 场景类型
     */
    private String sceneType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 扩展数据
     */
    private Map<String, Object> extraData;

    /**
     * 优先级（1-低 2-中 3-高 4-紧急）
     */
    private Integer priority;

    /**
     * 优先级描述
     */
    private String priorityDesc;

    /**
     * 发送时间
     */
    private Long sendTime;

    /**
     * 创建时间
     */
    private Long createTime;
}
