package com.subfg.domain.vo;

import java.math.BigDecimal;
import java.util.List;

import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 团长视角家庭组详情返回对象
 * 包含家庭组信息、成员信息列表和预计佣金信息
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Schema(description = "团长视角家庭组详情返回对象")
public class LeaderFamilyGroupDetailVo {
    
    /**
     * 家庭组信息
     */
    @Schema(description = "家庭组信息")
    private FgFamilyGroupPo familyGroup;

    /**
     * 家庭组成员列表
     */
    @Schema(description = "家庭组成员列表")
    private List<FgMemberPo> memberList;
    
    /**
     * 预计佣金信息
     */
    @Schema(description = "预计佣金信息")
    private CommissionInfo commissionInfo;
    
    /**
     * 佣金信息内部类
     */
    @Data
    @Schema(description = "佣金信息")
    public static class CommissionInfo {
        
        /**
         * 家庭组订阅价格
         */
        @Schema(description = "家庭组订阅价格", example = "99.99")
        private BigDecimal subscriptionAmount;
        
        /**
         * 佣金比例（百分比）
         */
        @Schema(description = "佣金比例（百分比）", example = "5.0")
        private BigDecimal commissionRate;
        
        /**
         * 预计佣金金额
         */
        @Schema(description = "预计佣金金额", example = "4.99")
        private BigDecimal estimatedCommission;
        
        /**
         * 计费周期（月）
         */
        @Schema(description = "计费周期（月）", example = "1")
        private Integer billingCycle;
        
        /**
         * 佣金说明
         */
        @Schema(description = "佣金说明", example = "按家庭组订阅价格的5%计算")
        private String description;
    }
}
