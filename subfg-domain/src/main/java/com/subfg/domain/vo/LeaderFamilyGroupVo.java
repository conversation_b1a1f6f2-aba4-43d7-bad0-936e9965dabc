package com.subfg.domain.vo;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 团长管理家庭组返回对象
 * 用于团长查看自己管理的家庭组列表，包含未激活团员统计信息
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Schema(description = "团长管理家庭组返回对象")
public class LeaderFamilyGroupVo {

    /**
     * 家庭组ID
     */
    @Schema(description = "家庭组ID")
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @Schema(description = "家庭组名称")
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    @Schema(description = "家庭组描述")
    private String description;

    /**
     * 家庭组类型（1-自建家庭组，2-拼团家庭组）
     */
    @Schema(description = "家庭组类型（1-自建家庭组，2-拼团家庭组）")
    private Integer groupType;

    /**
     * 家庭组类型名称
     */
    @Schema(description = "家庭组类型名称")
    private String groupTypeName;

    /**
     * 家庭组状态（0-审核中，1-组建中，2-发车中，3-关闭，-1-审核未通过）
     */
    @Schema(description = "家庭组状态（0-审核中，1-组建中，2-发车中，3-关闭，-1-审核未通过）")
    private Integer familyGroupStatus;

    /**
     * 家庭组状态名称
     */
    @Schema(description = "家庭组状态名称")
    private String statusName;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Integer productId;

    /**
     * 地区ID
     */
    @Schema(description = "地区ID")
    private Integer regionId;

    /**
     * 套餐ID
     */
    @Schema(description = "套餐ID")
    private Integer planId;

    /**
     * 订阅价格
     */
    @Schema(description = "订阅价格")
    private BigDecimal amount;

    /**
     * 计费周期（1-1个月，2-3个月（季度），3-6个月（半年），4-12个月（1年），5-24个月（2年），6-36个月（3年））
     */
    @Schema(description = "计费周期（月）")
    private Integer billingCycle;

    /**
     * 当前成员数
     */
    @Schema(description = "当前成员数")
    private Integer currentMemberCount;

    /**
     * 总空位数
     */
    @Schema(description = "总空位数")
    private Integer sumVacancy;

    /**
     * 截止发车时间（拼团家庭组专用）
     */
    @Schema(description = "截止发车时间（拼团家庭组专用）")
    private Long deadline;

    /**
     * 团长用户ID
     */
    @Schema(description = "团长用户ID")
    private String groupLeaderId;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createUserId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Long updateTime;

    /**
     * 最新加入时间（自建家庭组专用）
     */
    @Schema(description = "最新加入时间（自建家庭组专用）")
    private Long latestJoinTime;

    /**
     * 发车时间（拼团家庭组专用）
     */
    @Schema(description = "发车时间（拼团家庭组专用）")
    private Long launchTime;

    /**
     * 是否已转换（0-未转换，1-已转换，拼团家庭组专用）
     */
    @Schema(description = "是否已转换（0-未转换，1-已转换，拼团家庭组专用）")
    private Integer isConverted;

    // ==================== 团员激活状态统计信息 ====================

    /**
     * 是否有团员暂未激活
     * true-有未激活团员，false-所有团员都已激活
     */
    @Schema(description = "是否有团员暂未激活（true-有未激活团员，false-所有团员都已激活）")
    private Boolean hasPendingMembers;

    /**
     * 未激活团员数量
     */
    @Schema(description = "未激活团员数量")
    private Integer pendingMemberCount;

    /**
     * 已激活团员数量
     */
    @Schema(description = "已激活团员数量")
    private Integer activatedMemberCount;

    /**
     * 团员激活率（百分比，保留两位小数）
     * 计算公式：已激活团员数 / 总团员数 * 100
     */
    @Schema(description = "团员激活率（百分比，保留两位小数）")
    private BigDecimal activationRate;

}
