package com.subfg.domain.vo;

import lombok.Data;

/**
 * 未读消息数量响应VO
 */
@Data
public class UnreadCountVo {

    /**
     * 总未读消息数量
     */
    private Long totalUnreadCount;

    /**
     * 系统通知未读数量
     */
    private Long systemUnreadCount;

    /**
     * 业务通知未读数量
     */
    private Long businessUnreadCount;

    public UnreadCountVo() {
        this.totalUnreadCount = 0L;
        this.systemUnreadCount = 0L;
        this.businessUnreadCount = 0L;
    }

    public UnreadCountVo(Long totalUnreadCount, Long systemUnreadCount, Long businessUnreadCount) {
        this.totalUnreadCount = totalUnreadCount != null ? totalUnreadCount : 0L;
        this.systemUnreadCount = systemUnreadCount != null ? systemUnreadCount : 0L;
        this.businessUnreadCount = businessUnreadCount != null ? businessUnreadCount : 0L;
    }
}
