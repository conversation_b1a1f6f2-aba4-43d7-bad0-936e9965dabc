package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 支付方式枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum PaymentMethod {

    /**
     * 钱包支付
     */
    WALLET(1, "钱包支付", "使用平台钱包余额支付"),

    /**
     * 微信支付
     */
    WECHAT_PAY(2, "微信支付", "微信支付"),

    /**
     * 支付宝
     */
    ALIPAY(3, "支付宝", "支付宝支付"),

    /**
     * 银行卡
     */
    BANK_CARD(4, "银行卡", "银行卡支付"),

    /**
     * PayPal
     */
    PAYPAL(5, "PayPal", "PayPal支付"),

    /**
     * 信用卡
     */
    CREDIT_CARD(6, "信用卡", "信用卡支付"),

    /**
     * Apple Pay
     */
    APPLE_PAY(7, "Apple Pay", "Apple Pay支付"),

    /**
     * Google Pay
     */
    GOOGLE_PAY(8, "Google Pay", "Google Pay支付"),

    /**
     * 混合支付
     */
    MIXED_PAYMENT(9, "混合支付", "多种支付方式组合");

    /**
     * 方式代码（数据库存储值）
     */
    private final Integer code;

    /**
     * 方式名称（显示名称）
     */
    private final String name;

    /**
     * 方式描述
     */
    private final String description;

    PaymentMethod(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 方式代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static PaymentMethod fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (PaymentMethod method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 方式代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 判断是否为第三方支付
     *
     * @return 是否为第三方支付
     */
    public boolean isThirdPartyPayment() {
        return this == WECHAT_PAY || this == ALIPAY || this == PAYPAL || 
               this == APPLE_PAY || this == GOOGLE_PAY;
    }

    /**
     * 判断是否为平台内支付
     *
     * @return 是否为平台内支付
     */
    public boolean isPlatformPayment() {
        return this == WALLET;
    }

    /**
     * 判断是否为银行卡类支付
     *
     * @return 是否为银行卡类支付
     */
    public boolean isBankCardPayment() {
        return this == BANK_CARD || this == CREDIT_CARD;
    }
}
