package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 钱包交易类型枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum FundTransactionType {

    /**
     * 收入
     */
    INCOME(1, "收入", "资金收入"),

    /**
     * 支出
     */
    EXPENSE(2, "支出", "资金支出"),

    /**
     * 提现
     */
    WITHDRAW(3, "提现", "提现到外部账户");

    /**
     * 类型代码（数据库存储值）
     */
    private final Integer code;

    /**
     * 类型名称（显示名称）
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    FundTransactionType(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static FundTransactionType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (FundTransactionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有类型代码
     *
     * @return 类型代码数组
     */
    public static Integer[] getAllCodes() {
        FundTransactionType[] types = values();
        Integer[] codes = new Integer[types.length];
        for (int i = 0; i < types.length; i++) {
            codes[i] = types[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有类型名称
     *
     * @return 类型名称数组
     */
    public static String[] getAllNames() {
        FundTransactionType[] types = values();
        String[] names = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            names[i] = types[i].getName();
        }
        return names;
    }

    /**
     * 判断是否为收入类型
     *
     * @return 是否为收入类型
     */
    public boolean isIncome() {
        return this == INCOME;
    }

    /**
     * 判断是否为支出类型
     *
     * @return 是否为支出类型
     */
    public boolean isExpense() {
        return this == EXPENSE;
    }

    /**
     * 判断是否为提现类型
     *
     * @return 是否为提现类型
     */
    public boolean isWithdraw() {
        return this == WITHDRAW;
    }
}
