package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 邮箱验证码类型枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum EmailCodeType {

    /**
     * 注册验证码
     */
    REGISTER(1, "注册账号", "【SUBFG】注册验证码", ""),

    /**
     * 登录验证码
     */
    LOGIN(2, "登录账号", "【SUBFG】登录验证码", ""),

    /**
     * 密码重置验证码
     */
    RESET_PASSWORD(3, "重置密码", "【SUBFG】密码重置验证码", "reset_password:"),

    /**
     * 换绑邮箱验证码
     */
    CHANGE_EMAIL(4, "换绑邮箱", "【SUBFG】换绑邮箱验证码", "change_email:"),

    /**
     * 修改密码验证码
     */
    CHANGE_PASSWORD(5, "修改密码", "【SUBFG】修改密码验证码", "change_password:");

    /**
     * 类型代码（数据库存储值）
     */
    private final Integer code;

    /**
     * 操作描述（用于邮件内容）
     */
    private final String description;

    /**
     * 邮件主题
     */
    private final String subject;

    /**
     * Redis缓存键前缀
     */
    private final String redisKeyPrefix;

    EmailCodeType(Integer code, String description, String subject, String redisKeyPrefix) {
        this.code = code;
        this.description = description;
        this.subject = subject;
        this.redisKeyPrefix = redisKeyPrefix;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static EmailCodeType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (EmailCodeType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有类型代码
     *
     * @return 类型代码数组
     */
    public static Integer[] getAllCodes() {
        EmailCodeType[] types = values();
        Integer[] codes = new Integer[types.length];
        for (int i = 0; i < types.length; i++) {
            codes[i] = types[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有操作描述
     *
     * @return 操作描述数组
     */
    public static String[] getAllDescriptions() {
        EmailCodeType[] types = values();
        String[] descriptions = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            descriptions[i] = types[i].getDescription();
        }
        return descriptions;
    }

    /**
     * 获取默认邮件主题（当类型未知时使用）
     *
     * @return 默认邮件主题
     */
    public static String getDefaultSubject() {
        return "【SUBFG】验证码";
    }

    /**
     * 获取默认操作描述（当类型未知时使用）
     *
     * @return 默认操作描述
     */
    public static String getDefaultDescription() {
        return "验证身份";
    }

    /**
     * 构建Redis缓存键
     * 格式：RedisConstants.EMAIL_CODE_KEY + 缓存键前缀 + email
     *
     * @param redisConstantsKey Redis常量键（RedisConstants.EMAIL_CODE_KEY）
     * @param email 邮箱地址
     * @return 完整的Redis缓存键
     */
    public String buildRedisKey(String redisConstantsKey, String email) {
        return redisConstantsKey + this.redisKeyPrefix + email;
    }

    /**
     * 获取Redis键前缀
     *
     * @return Redis键前缀
     */
    public String getRedisKeyPrefix() {
        return this.redisKeyPrefix;
    }
}
