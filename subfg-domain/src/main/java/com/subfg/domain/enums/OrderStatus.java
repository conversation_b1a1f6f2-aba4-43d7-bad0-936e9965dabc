package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 订单状态枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum OrderStatus {

    /**
     * 待支付
     */
    PENDING_PAYMENT(1, "待支付", "订单已创建，等待支付"),

    /**
     * 支付中
     */
    PAYING(2, "支付中", "正在处理支付"),

    /**
     * 已支付
     */
    PAID(3, "已支付", "支付成功，等待处理"),

    /**
     * 处理中
     */
    PROCESSING(4, "处理中", "订单正在处理"),

    /**
     * 已完成
     */
    COMPLETED(5, "已完成", "订单已完成"),

    /**
     * 已取消
     */
    CANCELLED(6, "已取消", "订单已取消"),

    /**
     * 已退款
     */
    REFUNDED(7, "已退款", "订单已退款"),

    /**
     * 退款中
     */
    REFUNDING(8, "退款中", "正在处理退款"),

    /**
     * 已过期
     */
    EXPIRED(9, "已过期", "订单已过期"),

    /**
     * 支付失败
     */
    PAYMENT_FAILED(10, "支付失败", "支付失败");

    /**
     * 状态代码（数据库存储值）
     */
    private final Integer code;

    /**
     * 状态名称（显示名称）
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    OrderStatus(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static OrderStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (OrderStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 判断是否为待支付状态
     *
     * @return 是否为待支付状态
     */
    public boolean isPendingPayment() {
        return this == PENDING_PAYMENT;
    }

    /**
     * 判断是否为支付相关状态
     *
     * @return 是否为支付相关状态
     */
    public boolean isPaymentRelated() {
        return this == PENDING_PAYMENT || this == PAYING || this == PAID || this == PAYMENT_FAILED;
    }

    /**
     * 判断是否为最终状态（不可再变更）
     *
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == REFUNDED || this == EXPIRED;
    }

    /**
     * 判断是否可以取消
     *
     * @return 是否可以取消
     */
    public boolean isCancellable() {
        return this == PENDING_PAYMENT || this == PAYMENT_FAILED;
    }

    /**
     * 判断是否可以退款
     *
     * @return 是否可以退款
     */
    public boolean isRefundable() {
        return this == PAID || this == PROCESSING || this == COMPLETED;
    }
}
