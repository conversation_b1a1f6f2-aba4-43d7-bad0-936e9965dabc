package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 订单类型枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum OrderType {

    /**
     * 自建家庭组订单
     */
    SELF_BUILT_FAMILY_GROUP(1, "自建家庭组订单", "自建家庭组订阅产品"),

    /**
     * 拼团家庭组订单
     */
    GROUP_BUYING_FAMILY_GROUP(2, "拼团家庭组订单", "拼团家庭组订阅产品"),

    /**
     * 续费订单
     */
    RENEWAL_ORDER(3, "续费订单", "订阅续费订单");

    /**
     * 类型代码（数据库存储值）
     */
    private final Integer code;

    /**
     * 类型名称（显示名称）
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    OrderType(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static OrderType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (OrderType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 判断是否为订阅类型
     *
     * @return 是否为订阅类型
     */
    public boolean isSubscriptionType() {
        return this == SELF_BUILT_FAMILY_GROUP ||
               this == GROUP_BUYING_FAMILY_GROUP ||
               this == RENEWAL_ORDER;
    }

    /**
     * 判断是否为家庭组相关类型
     *
     * @return 是否为家庭组相关类型
     */
    public boolean isFamilyGroupType() {
        return this == SELF_BUILT_FAMILY_GROUP || this == GROUP_BUYING_FAMILY_GROUP;
    }

    /**
     * 判断是否为续费类型
     *
     * @return 是否为续费类型
     */
    public boolean isRenewalType() {
        return this == RENEWAL_ORDER;
    }

    /**
     * 判断是否为自建家庭组类型
     *
     * @return 是否为自建家庭组类型
     */
    public boolean isSelfBuiltType() {
        return this == SELF_BUILT_FAMILY_GROUP;
    }

    /**
     * 判断是否为拼团家庭组类型
     *
     * @return 是否为拼团家庭组类型
     */
    public boolean isGroupBuyingType() {
        return this == GROUP_BUYING_FAMILY_GROUP;
    }
}
