package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 钱包交易状态枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum FundTransactionStatus {

    /**
     * 处理中
     */
    PENDING(0, "处理中", "交易正在处理中"),

    /**
     * 成功
     */
    SUCCESS(1, "成功", "交易成功完成"),

    /**
     * 失败
     */
    FAILED(2, "失败", "交易失败");

    /**
     * 状态代码（数据库存储值）
     */
    private final Integer code;

    /**
     * 状态名称（显示名称）
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    FundTransactionStatus(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static FundTransactionStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (FundTransactionStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有状态代码
     *
     * @return 状态代码数组
     */
    public static Integer[] getAllCodes() {
        FundTransactionStatus[] statuses = values();
        Integer[] codes = new Integer[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            codes[i] = statuses[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有状态名称
     *
     * @return 状态名称数组
     */
    public static String[] getAllNames() {
        FundTransactionStatus[] statuses = values();
        String[] names = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            names[i] = statuses[i].getName();
        }
        return names;
    }

    /**
     * 判断是否为处理中状态
     *
     * @return 是否为处理中状态
     */
    public boolean isPending() {
        return this == PENDING;
    }

    /**
     * 判断是否为成功状态
     *
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 判断是否为失败状态
     *
     * @return 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 判断是否为最终状态（成功或失败）
     *
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }
}
