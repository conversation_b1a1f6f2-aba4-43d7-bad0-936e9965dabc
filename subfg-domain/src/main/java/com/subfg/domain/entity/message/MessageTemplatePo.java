package com.subfg.domain.entity.message;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 消息模板实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("message_template")
public class MessageTemplatePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID（主键）
     */
    @TableId(value = "template_id", type = IdType.INPUT)
    private String templateId;

    /**
     * 模板名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 场景类型
     */
    @TableField("scene_type")
    private String sceneType;

    /**
     * 消息类型（1-系统通知 2-业务通知）
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 标题模板（支持变量占位符）
     */
    @TableField("title_template")
    private String titleTemplate;

    /**
     * 内容模板（支持变量占位符）
     */
    @TableField("content_template")
    private String contentTemplate;

    /**
     * 所需变量定义（JSON格式）
     */
    @TableField(value = "required_variables", typeHandler = JacksonTypeHandler.class)
    private java.util.List<String> requiredVariables;

    /**
     * 是否启用（1-启用 0-禁用）
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;
}
