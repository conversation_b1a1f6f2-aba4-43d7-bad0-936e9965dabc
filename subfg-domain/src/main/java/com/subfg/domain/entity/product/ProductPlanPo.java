package com.subfg.domain.entity.product;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 产品套餐实体类
 * 对应数据库表：product_plan
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_plan")
public class ProductPlanPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 套餐名称
     */
    @TableField("name")
    private String name;

    /**
     * 周期
     */
    @TableField("cycle")
    private Integer cycle;

    /**
     * 免费天数
     */
    @TableField("free_day")
    private Integer freeDay;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 预订金额
     */
    @TableField("booking_amount")
    private BigDecimal bookingAmount;

    /**
     * 年度金额
     */
    @TableField("year_amount")
    private BigDecimal yearAmount;

    /**
     * 年度原价
     */
    @TableField("year_original_amount")
    private BigDecimal yearOriginalAmount;

    /**
     * 原价
     */
    @TableField("original_amount")
    private BigDecimal originalAmount;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 空位数
     */
    @TableField("vacancy")
    private Integer vacancy;

    /**
     * 套餐URL
     */
    @TableField("plan_url")
    private String planUrl;

    /**
     * 描述ID
     */
    @TableField("describe_id")
    private Integer describeId;

    /**
     * 货币ID
     */
    @TableField("currency_id")
    private String currencyId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;

}
