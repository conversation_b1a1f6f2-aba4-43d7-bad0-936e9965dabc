package com.subfg.domain.entity.user;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户资金实体类
 * 对应数据库表：user_fund
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_fund")
public class UserFundPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 钱包ID（主键）
     */
    @TableId(value = "fund_id", type = IdType.INPUT)
    private String fundId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 可提现金额(可用佣金)
     */
    @TableField("withdraw_amount")
    private BigDecimal withdrawAmount;

    /**
     * 货币类型
     */
    @TableField("currency_type")
    private String currencyType;

    /**
     * 总收入
     */
    @TableField("total_income")
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    @TableField("total_expense")
    private BigDecimal totalExpense;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
