package com.subfg.domain.entity.user;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户资金流水实体类
 * 对应数据库表：user_fund_transaction
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_fund_transaction")
public class UserFundTransactionPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流水ID（主键）
     */
    @TableId(value = "transaction_id", type = IdType.INPUT)
    private String transactionId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 交易类型（1:收入 2:支出 3:提现）
     */
    @TableField("transaction_type")
    private Integer transactionType;

    /**
     * 交易金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 交易前余额
     */
    @TableField("balance_before")
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    @TableField("balance_after")
    private BigDecimal balanceAfter;

    /**
     * 交易状态（0:处理中 1:成功 2:失败）
     */
    @TableField("status")
    private Integer status;

    /**
     * 关联订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 货币类型
     */
    @TableField("currency_type")
    private String currencyType;

    /**
     * 交易备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 第三方交易号
     */
    @TableField("third_transaction_no")
    private String thirdTransactionNo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    private Long completeTime;

    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
