package com.subfg.domain.entity.order;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 订单实体类
 * 对应数据库表：order
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order")
public class OrderPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID（主键）
     */
    @TableId(value = "order_id", type = IdType.INPUT)
    private String orderId;

    /**
     * 订单号（业务编号，对外展示）
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 订单类型（1:自建家庭组订单 2:拼团家庭组订单 3:续费订单）
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 订单状态（1:待支付 2:支付中 3:已支付 4:处理中 5:已完成 6:已取消 7:已退款 8:退款中 9:已过期 10:支付失败）
     */
    @TableField("order_status")
    private Integer orderStatus;

    // ==================== 产品信息 ====================

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 产品名称（冗余字段，便于查询）
     */
    @TableField("product_name")
    private String productName;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 套餐名称（冗余字段）
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 计费周期（月）
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    // ==================== 家庭组信息 ====================

    /**
     * 家庭组ID（如果是家庭组订单）
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 成员ID（如果是家庭组成员订单）
     */
    @TableField("member_id")
    private String memberId;

    // ==================== 金额信息 ====================

    /**
     * 订单原价
     */
    @TableField("original_amount")
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 订单总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 实际支付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;

    /**
     * 货币类型
     */
    @TableField("currency_type")
    private String currencyType;

    // ==================== 支付信息 ====================

    /**
     * 支付方式（1:钱包支付 2:微信支付 3:支付宝 4:银行卡 5:PayPal 6:信用卡 7:Apple Pay 8:Google Pay 9:混合支付）
     */
    @TableField("payment_method")
    private Integer paymentMethod;

    /**
     * 第三方支付单号
     */
    @TableField("third_party_order_no")
    private String thirdPartyOrderNo;

    /**
     * 第三方交易号
     */
    @TableField("third_party_transaction_no")
    private String thirdPartyTransactionNo;

    // ==================== 优惠信息 ====================

    /**
     * 优惠券ID
     */
    @TableField("coupon_id")
    private String couponId;

    /**
     * 优惠券代码
     */
    @TableField("coupon_code")
    private String couponCode;


    // ==================== 订单描述 ====================

    /**
     * 订单标题
     */
    @TableField("order_title")
    private String orderTitle;

    /**
     * 订单描述
     */
    @TableField("order_description")
    private String orderDescription;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ==================== 时间信息 ====================

    /**
     * 订单过期时间
     */
    @TableField("expire_time")
    private Long expireTime;

    /**
     * 支付时间
     */
    @TableField("paid_time")
    private Long paidTime;

    /**
     * 完成时间
     */
    @TableField("completed_time")
    private Long completedTime;

    /**
     * 取消时间
     */
    @TableField("cancelled_time")
    private Long cancelledTime;

    /**
     * 退款时间
     */
    @TableField("refunded_time")
    private Long refundedTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
