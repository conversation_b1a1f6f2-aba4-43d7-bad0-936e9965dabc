package com.subfg.domain.entity.message;

import java.io.Serializable;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户消息记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_message")
public class UserMessagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID（主键）
     */
    @TableId(value = "message_id", type = IdType.INPUT)
    private String messageId;

    /**
     * 接收用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 消息标题
     */
    @TableField("title")
    private String title;

    /**
     * 消息内容
     */
    @TableField("content")
    private String content;

    /**
     * 消息类型（1-系统通知 2-业务通知）
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 已读状态（0-未读 1-已读）
     */
    @TableField("read_status")
    private Integer readStatus;

    /**
     * 阅读时间
     */
    @TableField("read_time")
    private Long readTime;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 场景类型
     */
    @TableField("scene_type")
    private String sceneType;

    /**
     * 业务ID
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 扩展数据
     */
    @TableField(value = "extra_data", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extraData;

    /**
     * 优先级（1-低 2-中 3-高 4-紧急）
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private Long sendTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;
    
}
