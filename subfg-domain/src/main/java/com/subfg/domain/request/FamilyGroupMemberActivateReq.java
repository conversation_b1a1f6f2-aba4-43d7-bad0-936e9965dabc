package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 团长激活成员请求
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Schema(description = "团长激活成员请求")
public class FamilyGroupMemberActivateReq {

    /**
     * 家庭组ID
     */
    @Schema(description = "家庭组ID")
    @NotBlank(message = "家庭组ID不能为空")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 激活备注
     */
    @Schema(description = "激活备注")
    private String remark;
}
