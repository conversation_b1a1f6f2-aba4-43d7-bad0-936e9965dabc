package com.subfg.domain.request;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 成员批量管理请求
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Schema(description = "成员批量管理请求")
public class MemberManageReq {

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    @Schema(description = "操作类型（ACTIVATE-激活，REMOVE-移除，UPDATE_STATUS-更新状态）", required = true)
    private String action;

    /**
     * 目标用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @Schema(description = "目标用户ID列表", required = true)
    private List<String> userIds;

    /**
     * 目标状态（当action为UPDATE_STATUS时使用）
     */
    @Schema(description = "目标状态")
    private Integer targetStatus;

    /**
     * 操作原因
     */
    @Schema(description = "操作原因")
    private String reason;

    /**
     * 是否发送通知
     */
    @Schema(description = "是否发送通知", defaultValue = "true")
    private Boolean sendNotification = true;

    /**
     * 操作类型常量
     */
    public static class Action {
        /** 激活成员 */
        public static final String ACTIVATE = "ACTIVATE";
        /** 移除成员 */
        public static final String REMOVE = "REMOVE";
        /** 更新状态 */
        public static final String UPDATE_STATUS = "UPDATE_STATUS";
    }
}
