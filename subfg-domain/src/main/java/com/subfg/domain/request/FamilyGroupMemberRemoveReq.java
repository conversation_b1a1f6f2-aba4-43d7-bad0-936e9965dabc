package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class FamilyGroupMemberRemoveReq {

    /**
     * 家庭组ID
     */
    @Schema(description = "家庭组ID")
    @NotBlank(message = "家庭组ID不能为空")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 移除原因
     */
    @Schema(description = "移除原因")
    private String removeReason;
}
