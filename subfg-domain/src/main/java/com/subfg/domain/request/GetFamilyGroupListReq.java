package com.subfg.domain.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 获取家庭组列表请求参数
 */
@Data
public class GetFamilyGroupListReq {

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 家庭组类型（1-自建，2-拼团，不传查询所有）
     */
    private Integer groupType;

    /**
     * 家庭组状态（不传查询所有）
     */
    private Integer status;

    /**
     * 产品ID（不传查询所有）
     */
    private Integer productId;

    /**
     * 地区ID（不传查询所有）
     */
    private Integer regionId;

    /**
     * 团长用户ID（不传查询所有）
     */
    private String groupLeaderId;

    /**
     * 创建用户ID（不传查询所有）
     */
    private String createUserId;

    /**
     * 家庭组名称关键字（模糊搜索）
     */
    private String keyword;

}
