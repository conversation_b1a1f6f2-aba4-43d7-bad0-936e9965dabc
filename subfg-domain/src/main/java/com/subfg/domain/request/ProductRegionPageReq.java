package com.subfg.domain.request;

import lombok.Data;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 产品地区分页查询请求参数
 */
@Data
public class ProductRegionPageReq {

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 地区名称关键字（模糊搜索）
     */
    private String keyword;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 地区代码
     */
    private String areaCode;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 是否热门
     */
    private Boolean popular;

}