package com.subfg.domain.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;

@Getter
public class LaunchGroupReq {

    /**
     * 家庭组ID
     */
    @NotBlank(message = "家庭组ID不能为空")
    private String familyGroupId;

    /**
     * 审核图片
     */
    @NotBlank(message = "审核图片不能为空")
    private String reviewPicture;

    /**
     * 审核备注
     */
    private String reviewRemark;

}
