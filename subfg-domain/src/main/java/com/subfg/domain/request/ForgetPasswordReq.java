package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 忘记密码请求
 */
@Data
@Schema(description = "忘记密码请求")
public class ForgetPasswordReq {

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    @NotBlank(message = "邮箱地址不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 邮箱验证码
     */
    @Schema(description = "邮箱验证码", example = "123456")
    @NotBlank(message = "邮箱验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须是6位数字")
    private String emailCode;

    /**
     * 新密码
     */
    @Schema(description = "新密码", example = "newPassword123")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码长度必须在6-20位之间")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$",
             message = "新密码必须包含字母和数字，可包含特殊字符@$!%*?&")
    private String newPassword;

    /**
     * 确认新密码
     */
    @Schema(description = "确认新密码", example = "newPassword123")
    @NotBlank(message = "确认新密码不能为空")
    private String confirmPassword;

}
