package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 修改密码请求
 */
@Data
@Schema(description = "修改密码请求")
public class ChangePasswordReq {

    /**
     * 修改方式（1-验证旧密码修改，2-邮箱验证码修改）
     */
    @Schema(description = "修改方式（1-验证旧密码修改，2-邮箱验证码修改）", example = "1")
    @NotBlank(message = "修改方式不能为空")
    @Pattern(regexp = "^[12]$", message = "修改方式只能是1或2")
    private String changeType;

    /**
     * 旧密码（当changeType=1时必填）
     */
    @Schema(description = "旧密码（当修改方式为1时必填）", example = "oldPassword123")
    private String oldPassword;

    /**
     * 邮箱验证码（当changeType=2时必填）
     */
    @Schema(description = "邮箱验证码（当修改方式为2时必填）", example = "123456")
    private String emailCode;

    /**
     * 新密码
     */
    @Schema(description = "新密码", example = "newPassword123")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码长度必须在6-20位之间")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$",
             message = "新密码必须包含字母和数字，可包含特殊字符@$!%*?&")
    private String newPassword;

    /**
     * 确认新密码
     */
    @Schema(description = "确认新密码", example = "newPassword123")
    @NotBlank(message = "确认新密码不能为空")
    private String confirmPassword;

}
