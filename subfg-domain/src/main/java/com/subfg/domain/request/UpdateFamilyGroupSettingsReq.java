package com.subfg.domain.request;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新家庭组设置请求
 * 用于团长完善审核信息
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Schema(description = "更新家庭组设置请求")
public class UpdateFamilyGroupSettingsReq {

    /**
     * 家庭组ID
     */
    @Schema(description = "家庭组ID", required = true)
    @NotBlank(message = "家庭组ID不能为空")
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @Schema(description = "家庭组名称", example = "我的家庭组")
    @Size(max = 50, message = "家庭组名称长度不能超过50个字符")
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    @Schema(description = "家庭组描述", example = "这是一个测试家庭组")
    @Size(max = 200, message = "家庭组描述长度不能超过200个字符")
    private String description;

    /**
     * 审核图片URL
     */
    @Schema(description = "审核图片URL", example = "https://example.com/review.jpg")
    @Size(max = 500, message = "审核图片URL长度不能超过500个字符")
    private String reviewPicture;

    /**
     * 家庭组标签
     */
    @Schema(description = "家庭组标签列表", example = "[\"学生优惠\", \"长期稳定\"]")
    private List<String> tags;

    /**
     * 更新备注
     */
    @Schema(description = "更新备注", example = "完善审核信息")
    @Size(max = 200, message = "更新备注长度不能超过200个字符")
    private String remark;
}
