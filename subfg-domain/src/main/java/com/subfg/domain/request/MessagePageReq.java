package com.subfg.domain.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 消息分页查询请求参数
 */
@Data
public class MessagePageReq {

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 读取状态筛选（0-未读 1-已读）
     */
    private Integer readStatus;

    /**
     * 消息类型筛选（1-系统通知 2-业务通知）
     */
    private Integer messageType;

    /**
     * 业务类型筛选
     */
    private String businessType;

    /**
     * 场景类型筛选
     */
    private String sceneType;

    /**
     * 优先级筛选（1-低 2-中 3-高 4-紧急）
     */
    private Integer priority;



    /**
     * 排序方式（sendTime-按发送时间，priority-按优先级，readStatus-按读取状态）
     */
    private String orderBy;
}
