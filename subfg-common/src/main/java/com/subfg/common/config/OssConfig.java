package com.subfg.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;

import lombok.Data;

/**
 * 阿里云OSS配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {

    /**
     * OSS服务的访问域名
     */
    private String endpoint;

    /**
     * 访问身份验证中用到的AccessKey ID
     */
    private String accessKeyId;

    /**
     * 访问身份验证中用到的AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * OSS的存储空间名称
     */
    private String bucketName;

    /**
     * 访问文件的URL前缀
     */
    private String urlPrefix;

    /**
     * 创建OSS客户端实例
     */
    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
} 