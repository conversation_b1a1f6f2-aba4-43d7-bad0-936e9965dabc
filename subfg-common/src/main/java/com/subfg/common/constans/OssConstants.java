package com.subfg.common.constans;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OSS相关常量
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
public class OssConstants {

    /**
     * OSS上传目录枚举
     */
    @Getter
    @AllArgsConstructor
    public enum UploadDirectory {
        
        /**
         * 用户头像目录
         */
        USER_AVATAR("subfg_v3/avatar/", "用户头像"),
        
        /**
         * 审核图片目录
         */
        AUDIT_IMAGE("subfg_v3/audit/", "审核图片");
        
        /**
         * 目录路径
         */
        private final String path;
        
        /**
         * 目录描述
         */
        private final String description;
    }
    
    /**
     * 支持的图片格式
     */
    public static final String[] SUPPORTED_IMAGE_FORMATS = {
        "jpg", "jpeg", "png", "gif", "bmp", "webp"
    };
    
    /**
     * 最大文件大小（10MB）
     */
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    /**
     * 默认文件名前缀
     */
    public static final String DEFAULT_FILE_PREFIX = "subfg_";
}
