package com.subfg.common.constans;

/**
 * 家庭组相关常量
 */
public class FamilyGroupConstants {

    /**
     * 家庭组状态
     */
    public static class Status {
        /** 审核中 */
        public static final int REVIEWING = 0;
        /** 组建中 */
        public static final int BUILDING = 1;
        /** 已发车 */
        public static final int LAUNCHED = 2;
        /** 关闭（团长关闭） 或 拼团失败 - 解散（截止发车时间未发车或无团长） */
        public static final int CLOSED = 3;
        /** 审核未通过 */
        public static final int REVIEW_REJECTED = -1;

    }

    /**
     * 成员状态
     */
    public static class MemberStatus {
        /** 待激活 */
        public static final int PENDING = 0;
        /** 已激活 */
        public static final int ACTIVATED = 1;
        /** 已退出 */
        public static final int LEFT = 2;
        /** 被移除 */
        public static final int REMOVED = 3;
        /** 已过期 */
        public static final int EXPIRED = 4;
    }

    /**
     * 家庭组类型
     */
    public static class GroupType {
        /** 自建家庭组 */
        public static final int SELF_BUILT = 1;
        /** 拼团家庭组 */
        public static final int GROUP_BUYING = 2;
    }

    /**
     * 来源类型
     */
    public static class SourceType {
        /** 直接创建 */
        public static final int DIRECT_CREATE = 1;
        /** 拼团转换 */
        public static final int GROUP_BUYING_CONVERT = 2;
    }

    /**
     * 默认值
     */
    public static class Defaults {
        /** 默认计费周期（月） */
        public static final int DEFAULT_BILLING_CYCLE = 1;
        /** 默认初始成员数（创建者） */
        public static final int DEFAULT_INITIAL_MEMBER_COUNT = 1;
    }

    /**
     * 计费周期
     */
    public static class BillingCycle {
        /** 1个月 */
        public static final int MONTHLY = 1;
        /** 3个月（季度） */
        public static final int QUARTERLY = 2;
        /** 6个月（半年） */
        public static final int SEMI_ANNUAL = 3;
        /** 12个月（1年） */
        public static final int ANNUAL = 4;
        /** 24个月（2年） */
        public static final int BIENNIAL = 5;
        /** 36个月（3年） */
        public static final int TRIENNIAL = 6;
        
    }

    /**
     * 错误消息代码
     */
    public static class ErrorCode {
        /** 家庭组不存在 */
        public static final String FAMILY_GROUP_NOT_FOUND = "family.group.not.found";
        /** 家庭组已满 */
        public static final String FAMILY_GROUP_FULL = "family.group.full";
        /** 已加入该家庭组 */
        public static final String ALREADY_JOINED = "family.group.already.joined";
        /** 未加入该家庭组 */
        public static final String NOT_JOINED = "family.group.not.joined";
        /** 无权限操作该家庭组 */
        public static final String PERMISSION_DENIED = "family.group.permission.denied";
        /** 创建家庭组失败 */
        public static final String CREATE_FAILED = "family.group.create.error";
        /** 加入家庭组失败 */
        public static final String JOIN_FAILED = "family.group.join.error";
        /** 离开家庭组失败 */
        public static final String LEAVE_FAILED = "family.group.leave.error";
        /** 超过最大创建数量 */
        public static final String EXCEED_MAX_CREATE_COUNT = "family.group.exceed.max.create.count";
        /** 超过最大加入数量 */
        public static final String EXCEED_MAX_JOIN_COUNT = "family.group.exceed.max.join.count";
        /** 家庭组名称过长 */
        public static final String NAME_TOO_LONG = "family.group.name.too.long";
        /** 家庭组描述过长 */
        public static final String DESCRIPTION_TOO_LONG = "family.group.description.too.long";
        /** 成员数量无效 */
        public static final String INVALID_MEMBER_COUNT = "family.group.invalid.member.count";
        /** 产品不存在 */
        public static final String PRODUCT_NOT_FOUND = "product.not.found";
        /** 产品不可用 */
        public static final String PRODUCT_UNAVAILABLE = "product.unavailable";
        /** 价格无效 */
        public static final String INVALID_AMOUNT = "family.group.invalid.amount";
        /** 计费周期无效 */
        public static final String INVALID_BILLING_CYCLE = "family.group.invalid.billing.cycle";
        /** 截止时间无效 */
        public static final String DEADLINE_INVALID = "family.group.deadline.invalid";
        /** 截止时间过远 */
        public static final String DEADLINE_TOO_FAR = "family.group.deadline.too.far";
    }

    /**
     * 成功消息代码
     */
    public static class SuccessCode {
        /** 家庭组创建成功 */
        public static final String CREATE_SUCCESS = "family.group.create.success";
        /** 加入家庭组成功 */
        public static final String JOIN_SUCCESS = "family.group.join.success";
        /** 离开家庭组成功 */
        public static final String LEAVE_SUCCESS = "family.group.leave.success";
    }
}
