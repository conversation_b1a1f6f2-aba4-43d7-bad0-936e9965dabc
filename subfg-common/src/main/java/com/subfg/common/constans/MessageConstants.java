package com.subfg.common.constans;

/**
 * 消息相关常量类
 */
public class MessageConstants {

    // ==================== 业务类型常量 ====================
    
    /**
     * 家庭组相关业务
     */
    public static final String BUSINESS_TYPE_FAMILY_GROUP = "FAMILY_GROUP";
    
    /**
     * 订单相关业务
     */
    public static final String BUSINESS_TYPE_ORDER = "ORDER";
    
    /**
     * 支付相关业务
     */
    public static final String BUSINESS_TYPE_PAYMENT = "PAYMENT";
    
    /**
     * 用户相关业务
     */
    public static final String BUSINESS_TYPE_USER = "USER";
    
    /**
     * 评论相关业务
     */
    public static final String BUSINESS_TYPE_COMMENT = "COMMENT";
    
    /**
     * 系统相关业务
     */
    public static final String BUSINESS_TYPE_SYSTEM = "SYSTEM";

    // ==================== 家庭组业务场景类型 ====================

    /**
     * 家庭组场景类型
     */
    public static class FamilyGroup {
        /** 邀请加入家庭组 */
        public static final String INVITE = "INVITE";

        /** 申请加入家庭组 */
        public static final String APPLY = "APPLY";

        /** 审批通过 */
        public static final String APPROVE = "APPROVE";

        /** 审批拒绝 */
        public static final String REJECT = "REJECT";

        /** 成员加入 */
        public static final String JOIN = "JOIN";

        /** 成员离开 */
        public static final String LEAVE = "LEAVE";

        /** 成员被移除 */
        public static final String REMOVE = "REMOVE";

        /** 角色变更 */
        public static final String ROLE_CHANGE = "ROLE_CHANGE";

        /** 家庭组解散 */
        public static final String DISSOLVE = "DISSOLVE";

        /** 家庭组信息更新 */
        public static final String UPDATE = "UPDATE";
    }

    // ==================== 订单业务场景类型 ====================

    /**
     * 订单场景类型
     */
    public static class Order {
        /** 订单创建 */
        public static final String CREATE = "CREATE";

        /** 订单确认 */
        public static final String CONFIRM = "CONFIRM";

        /** 订单支付 */
        public static final String PAYMENT = "PAYMENT";

        /** 订单发货 */
        public static final String SHIP = "SHIP";

        /** 订单完成 */
        public static final String COMPLETE = "COMPLETE";

        /** 订单取消 */
        public static final String CANCEL = "CANCEL";

        /** 订单退款 */
        public static final String REFUND = "REFUND";

        /** 订单超时 */
        public static final String TIMEOUT = "TIMEOUT";

        /** 订单异常 */
        public static final String EXCEPTION = "EXCEPTION";
    }

    // ==================== 支付业务场景类型 ====================

    /**
     * 支付场景类型
     */
    public static class Payment {
        /** 支付成功 */
        public static final String SUCCESS = "SUCCESS";

        /** 支付失败 */
        public static final String FAILED = "FAILED";

        /** 支付超时 */
        public static final String TIMEOUT = "TIMEOUT";

        /** 支付取消 */
        public static final String CANCEL = "CANCEL";

        /** 退款成功 */
        public static final String REFUND_SUCCESS = "REFUND_SUCCESS";

        /** 退款失败 */
        public static final String REFUND_FAILED = "REFUND_FAILED";

        /** 退款处理中 */
        public static final String REFUND_PROCESSING = "REFUND_PROCESSING";
    }

    // ==================== 用户业务场景类型 ====================

    /**
     * 用户场景类型
     */
    public static class User {
        /** 用户注册 */
        public static final String REGISTER = "REGISTER";

        /** 用户登录 */
        public static final String LOGIN = "LOGIN";

        /** 密码重置 */
        public static final String PASSWORD_RESET = "PASSWORD_RESET";

        /** 账号激活 */
        public static final String ACTIVATE = "ACTIVATE";

        /** 账号冻结 */
        public static final String FREEZE = "FREEZE";

        /** 账号解冻 */
        public static final String UNFREEZE = "UNFREEZE";

        /** 信息更新 */
        public static final String UPDATE = "UPDATE";

        /** 实名认证 */
        public static final String VERIFY = "VERIFY";
    }

    // ==================== 评论业务场景类型 ====================

    /**
     * 评论场景类型
     */
    public static class Comment {
        /** 新评论 */
        public static final String CREATE = "CREATE";

        /** 评论回复 */
        public static final String REPLY = "REPLY";

        /** 评论点赞 */
        public static final String LIKE = "LIKE";

        /** 评论被删除 */
        public static final String DELETE = "DELETE";

        /** 评论被举报 */
        public static final String REPORT = "REPORT";

        /** 评论审核通过 */
        public static final String APPROVE = "APPROVE";

        /** 评论审核拒绝 */
        public static final String REJECT = "REJECT";
    }

    // ==================== 系统业务场景类型 ====================

    /**
     * 系统场景类型
     */
    public static class System {
        /** 系统维护 */
        public static final String MAINTAIN = "MAINTAIN";

        /** 系统升级 */
        public static final String UPGRADE = "UPGRADE";

        /** 功能上线 */
        public static final String FEATURE_ONLINE = "FEATURE_ONLINE";

        /** 功能下线 */
        public static final String FEATURE_OFFLINE = "FEATURE_OFFLINE";

        /** 安全提醒 */
        public static final String SECURITY_ALERT = "SECURITY_ALERT";

        /** 公告通知 */
        public static final String ANNOUNCEMENT = "ANNOUNCEMENT";

        /** 活动通知 */
        public static final String ACTIVITY = "ACTIVITY";
    }

    // ==================== 消息类型常量 ====================
    
    /**
     * 系统通知
     */
    public static final int MESSAGE_TYPE_SYSTEM = 1;
    
    /**
     * 业务通知
     */
    public static final int MESSAGE_TYPE_BUSINESS = 2;

    // ==================== 读取状态常量 ====================
    
    /**
     * 未读
     */
    public static final int READ_STATUS_UNREAD = 0;
    
    /**
     * 已读
     */
    public static final int READ_STATUS_READ = 1;

    // ==================== 优先级常量 ====================
    
    /**
     * 低优先级
     */
    public static final int PRIORITY_LOW = 1;
    
    /**
     * 中优先级
     */
    public static final int PRIORITY_MEDIUM = 2;
    
    /**
     * 高优先级
     */
    public static final int PRIORITY_HIGH = 3;
    
    /**
     * 紧急优先级
     */
    public static final int PRIORITY_URGENT = 4;
}
