package com.subfg.common.util;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.UUID;

import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.subfg.common.config.OssConfig;
import com.subfg.common.constans.OssConstants;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * OSS工具类
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OssUtil {

    private final OSS ossClient;
    private final OssConfig ossConfig;

    /**
     * 上传文件到OSS
     *
     * @param file      要上传的文件
     * @param directory 上传目录枚举
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file, OssConstants.UploadDirectory directory) {
        try {
            // 验证文件
            validateFile(file);
            
            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());
            
            // 构建完整的文件路径
            String filePath = directory.getPath() + fileName;
            
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            
            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(), 
                filePath, 
                file.getInputStream(), 
                metadata
            );
            
            ossClient.putObject(putObjectRequest);
            
            // 构建访问URL
            String fileUrl = ossConfig.getUrlPrefix() + "/" + filePath;
            
            log.info("文件上传成功: {} -> {}", file.getOriginalFilename(), fileUrl);
            
            return fileUrl;
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件流到OSS
     *
     * @param inputStream 文件输入流
     * @param fileName    文件名
     * @param directory   上传目录枚举
     * @param contentType 文件类型
     * @return 文件访问URL
     */
    public String uploadFile(InputStream inputStream, String fileName, 
                           OssConstants.UploadDirectory directory, String contentType) {
        try {
            // 生成文件名
            String generatedFileName = generateFileName(fileName);
            
            // 构建完整的文件路径
            String filePath = directory.getPath() + generatedFileName;
            
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            
            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(), 
                filePath, 
                inputStream, 
                metadata
            );
            
            ossClient.putObject(putObjectRequest);
            
            // 构建访问URL
            String fileUrl = ossConfig.getUrlPrefix() + "/" + filePath;
            
            log.info("文件流上传成功: {} -> {}", fileName, fileUrl);
            
            return fileUrl;
            
        } catch (Exception e) {
            log.error("文件流上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件流上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除OSS文件
     *
     * @param fileUrl 文件URL
     */
    public void deleteFile(String fileUrl) {
        try {
            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            
            if (filePath != null) {
                ossClient.deleteObject(ossConfig.getBucketName(), filePath);
                log.info("文件删除成功: {}", fileUrl);
            } else {
                log.warn("无法从URL中提取文件路径: {}", fileUrl);
            }
            
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件
     *
     * @param file 要验证的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > OssConstants.MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }
        
        // 检查文件格式
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isValidImageFormat(originalFilename)) {
            throw new IllegalArgumentException("不支持的文件格式，仅支持: " + 
                Arrays.toString(OssConstants.SUPPORTED_IMAGE_FORMATS));
        }
    }

    /**
     * 检查是否为有效的图片格式
     *
     * @param fileName 文件名
     * @return 是否为有效格式
     */
    private boolean isValidImageFormat(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return Arrays.asList(OssConstants.SUPPORTED_IMAGE_FORMATS).contains(extension);
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 生成唯一文件名
     *
     * @param originalFilename 原始文件名
     * @return 生成的文件名
     */
    private String generateFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        return OssConstants.DEFAULT_FILE_PREFIX + timestamp + "_" + uuid + "." + extension;
    }

    /**
     * 从URL中提取文件路径
     *
     * @param fileUrl 文件URL
     * @return 文件路径
     */
    private String extractFilePathFromUrl(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(ossConfig.getUrlPrefix())) {
            return null;
        }
        
        return fileUrl.substring(ossConfig.getUrlPrefix().length() + 1);
    }
}
