package com.subfg.api.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.api.Serivce.UserFamilyGroupService;
import com.subfg.domain.request.GroupPageReq;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.domain.vo.MyJoinedFamilyGroupVo;
import com.subfg.domain.vo.Result;
import com.subfg.domain.vo.UserFamilyGroupDetailVo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/familyGroup/user")
@Tag(name = "团员(用户)家庭组管理", description = "团员(用户)个人家庭组相关接口")
public class FamilyGroupUserController {

    private final UserFamilyGroupService userFamilyGroupService;

    /**
     * 获取当前用户创建的家庭组分页列表
     * @param req 分页请求参数
     * @return 用户创建的家庭组分页列表
     */
    @Operation(summary = "获取当前用户创建的家庭组分页列表(暂不使用)", description = "获取当前登录用户创建的所有家庭组")
    @GetMapping("/getMyCreatedFamilyGroups")
    public Result<Page<FamilyGroupVo>> getMyCreatedFamilyGroups(@Valid @ModelAttribute GroupPageReq req){
        Page<FamilyGroupVo> page = userFamilyGroupService.getMyCreatedFamilyGroups(req);
        return Result.success(page);
    }

    /**
     * 获取当前用户加入的家庭组分页列表
     * @param req 分页请求参数
     * @return 用户加入的家庭组分页列表
     */ 
    @Operation(summary = "获取当前用户加入的家庭组分页列表(同行车队)", description = "获取当前登录用户加入的所有家庭组（不包括团长是自己）")
    @GetMapping("/getMyJoinedFamilyGroups")
    public Result<Page<MyJoinedFamilyGroupVo>> getMyJoinedFamilyGroups(@Valid @ModelAttribute GroupPageReq req){
        Page<MyJoinedFamilyGroupVo> page = userFamilyGroupService.getMyJoinedFamilyGroups(req);
        return Result.success(page);
    }

    /**
     * 获取当前用户加入的拼团家庭组分页列表
     * @param req 分页请求参数
     * @return 用户加入的拼团家庭组分页列表
     */
    @Operation(summary = "获取当前用户加入的拼团家庭组分页列表(拼团车队)", description = "获取当前登录用户加入的所有拼团家庭组")
    @GetMapping("/getMyJoinedGroupBuyingFamilyGroups")
    public Result<Page<FamilyGroupVo>> getMyJoinedGroupBuyingFamilyGroups(@Valid @ModelAttribute GroupPageReq req){
        Page<FamilyGroupVo> page = userFamilyGroupService.getMyJoinedGroupBuyingFamilyGroups(req);
        return Result.success(page);
    }

    /**
     * 用户加入家庭组
     */
    @Operation(summary = "用户加入家庭组", description = "用户加入家庭组")
    @GetMapping("/joinFamilyGroup")
    public Result<String> joinFamilyGroup(@RequestParam String familyGroupId){
        userFamilyGroupService.joinFamilyGroup(familyGroupId);
        return Result.success();
    }

    /**
     * 用户退出家庭组
     */
    @Operation(summary = "用户退出家庭组", description = "用户退出家庭组")
    @GetMapping("/exitFamilyGroup")
    public Result<String> exitFamilyGroup(@RequestParam String familyGroupId){
        userFamilyGroupService.exitFamilyGroup(familyGroupId);
        return Result.success();
    }

    /**
     * 获取家庭组详情信息
     * @param familyGroupId 家庭组ID
     * @return 家庭组详情信息
     */
    @Operation(summary = "获取家庭组详情信息(团员视角)", description = "获取家庭组详情信息")
    @GetMapping("/getFamilyGroupDetail")
    public Result<UserFamilyGroupDetailVo> getFamilyGroupDetail(@RequestParam String familyGroupId){
        UserFamilyGroupDetailVo vo = userFamilyGroupService.getFamilyGroupDetail(familyGroupId);
        return Result.success(vo);
    }

    /**
     * 成为团长
     */
    @Operation(summary = "成为团长", description = "成为团长")
    @GetMapping("/becomeLeader")
    public Result<String> becomeLeader(@RequestParam String familyGroupId){
        userFamilyGroupService.becomeLeader(familyGroupId);
        return Result.success();
    }

}
