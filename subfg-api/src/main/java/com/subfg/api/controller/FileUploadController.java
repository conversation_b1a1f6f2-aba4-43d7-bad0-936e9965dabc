package com.subfg.api.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.subfg.api.Serivce.FileService;
import com.subfg.common.constans.OssConstants;
import com.subfg.common.util.OssUtil;
import com.subfg.domain.vo.Result;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/file")
@RequiredArgsConstructor
@Tag(name = "文件上传", description = "文件上传相关接口")
public class FileUploadController {

    private final FileService fileService;

    private final OssUtil ossUtil;

    /**
     * 上传用户头像
     *
     * @param file 头像文件
     * @return 上传结果
     */
    @PostMapping("/upload/avatar")
    @Operation(summary = "上传用户头像", description = "上传用户头像到OSS")
    public Result<Void> uploadAvatar(
            @Parameter(description = "头像文件", required = true)
            @RequestParam("file") MultipartFile file) {
                fileService.uploadAvatar(file);
                return Result.success();
    }

    /**
     * 上传审核图片
     *
     * @param file 审核图片文件
     * @return 上传结果
     */
    @PostMapping("/upload/audit")
    @Operation(summary = "上传审核图片", description = "上传审核图片到OSS")
    public Result<Map<String, Object>> uploadAuditImage(
            @Parameter(description = "审核图片文件", required = true)
            @RequestParam("file") MultipartFile file) {

        try {
            String fileUrl = ossUtil.uploadFile(file, OssConstants.UploadDirectory.AUDIT_IMAGE);

            Map<String, Object> data = new HashMap<>();
            data.put("fileUrl", fileUrl);

            return Result.success(data);

        } catch (Exception e) {
            log.error("审核图片上传失败: {}", e.getMessage(), e);
            return Result.error("审核图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "从OSS删除文件")
    public Result<String> deleteFile(
            @Parameter(description = "文件URL", required = true)
            @RequestParam("fileUrl") String fileUrl) {

        try {
            ossUtil.deleteFile(fileUrl);
            return Result.success("文件删除成功");

        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return Result.error("文件删除失败: " + e.getMessage());
        }
    }
}
