package com.subfg.api.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.entity.product.ProductCategoryPo;
import com.subfg.domain.entity.product.ProductPo;
import com.subfg.domain.entity.product.ProductRegionPo;
import com.subfg.domain.request.ProductCategoryPageReq;
import com.subfg.domain.request.ProductPageReq;
import com.subfg.domain.request.ProductRegionPageReq;
import com.subfg.domain.vo.BillingCycleVo;
import com.subfg.domain.vo.Result;
import com.subfg.api.Serivce.ProductService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/product")
@Tag(name = "产品管理", description = "产品管理相关接口")
public class ProductController {

    private final ProductService productService;

    /**
     * 分页获取产品列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取产品列表", description = "分页获取产品列表")
    public Result<Page<ProductPo>> page(@Valid @ModelAttribute ProductPageReq req){
        return Result.success(productService.page(req));
    }

    /**
     * 分页获取产品分类列表
     */
    @GetMapping("/category/page")
    @Operation(summary = "分页获取产品分类列表", description = "分页获取产品分类列表")
    public Result<Page<ProductCategoryPo>> categoryPage(@Valid @ModelAttribute ProductCategoryPageReq req){
        return Result.success(productService.categoryPage(req));
    }

    /**
     * 分页获取国家列表
     */
    @GetMapping("/region/page")
    @Operation(summary = "分页获取国家列表", description = "分页获取国家列表")
    public Result<Page<ProductRegionPo>> regionPage(@Valid @ModelAttribute ProductRegionPageReq req){
        return Result.success(productService.regionPage(req));
    }

    /**
     * 获取计费周期列表
     */
    @GetMapping("/billing/cycle/list")
    @Operation(summary = "获取计费周期列表", description = "获取计费周期列表")
    public Result<List<BillingCycleVo>> billingCycleList(){
        return Result.success(productService.billingCycleList());
    }
}
