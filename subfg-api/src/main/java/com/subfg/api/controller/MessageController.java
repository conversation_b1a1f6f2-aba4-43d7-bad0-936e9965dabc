package com.subfg.api.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.subfg.api.Serivce.MessageService;
import com.subfg.domain.request.MessagePageReq;
import com.subfg.domain.vo.Result;
import com.subfg.domain.vo.UnreadCountVo;
import com.subfg.domain.vo.UserMessageVo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/message")
@Tag(name = "消息管理", description = "消息管理相关接口")
public class MessageController {

    private final MessageService messageService;

    /**
     * 获取当前用户未读消息数量
     */
    @GetMapping("/unread/count")
    @Operation(summary = "获取当前用户未读消息数量", description = "获取当前用户未读消息数量统计")
    public Result<UnreadCountVo> getUnreadCount() {
        UnreadCountVo unreadCount = messageService.getUnreadCount();
        return Result.success(unreadCount);
    }

    /**
     * 分页查询当前用户消息列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询当前用户消息列表", description = "分页查询当前用户消息列表，支持按读取状态、消息类型等筛选")
    public Result<IPage<UserMessageVo>> getMessagePage(@Valid @ModelAttribute MessagePageReq req) {
        IPage<UserMessageVo> messagePage = messageService.getMessagePage(req);
        return Result.success(messagePage);
    }
}
