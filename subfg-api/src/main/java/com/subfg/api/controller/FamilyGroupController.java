package com.subfg.api.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.request.CreateGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.CreateSelfBuiltFamilyGroupReq;
import com.subfg.domain.request.GetFamilyGroupListReq;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.domain.vo.Result;
import com.subfg.api.Serivce.FamilyGroupService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/familyGroup")
@Tag(name = "家庭群组管理", description = "家庭群组管理相关接口")
public class FamilyGroupController {

    private final FamilyGroupService familyGroupService;

    /**
     * 创建自建家庭组
     * @param req
     * @return
     */
    @Operation(summary = "创建自建家庭群组", description = "创建自建家庭群组")
    @PostMapping("/createSelfBuiltFamilyGroup")
    public Result<String> createSelfBuiltFamilyGroup(@Valid @RequestBody CreateSelfBuiltFamilyGroupReq req){
        String familyGroupId = familyGroupService.createSelfBuiltFamilyGroup(req);
        return Result.success(familyGroupId);
        }

    /**
     * 创建拼团家庭组
     * @param req
     * @return
     */
    @Operation(summary = "创建拼团家庭群组", description = "创建拼团家庭群组")
    @PostMapping("/createGroupBuyingFamilyGroup")
    public Result<String> createGroupBuyingFamilyGroup(@Valid @RequestBody CreateGroupBuyingFamilyGroupReq req){
        String familyGroupId = familyGroupService.createGroupBuyingFamilyGroup(req);
        return Result.success(familyGroupId);
    }

    /**
     * 获取家庭组分页列表
     */
    @Operation(summary = "获取家庭组分页列表", description = "获取家庭组分页列表")
    @GetMapping("/getFamilyGroupList")
    public Result<Page<FamilyGroupVo>> getFamilyGroupList(@Valid @ModelAttribute GetFamilyGroupListReq req){
        Page<FamilyGroupVo> page = familyGroupService.getFamilyGroupList(req);
        return Result.success(page);
    }
    
    /**
     * 获取随机推荐家庭组列表
     */
    @Operation(summary = "获取随机推荐家庭组列表", description = "获取随机推荐家庭组列表")
    @GetMapping("/getRecommendFamilyGroupList")
    public Result<List<FamilyGroupVo>> getRecommendFamilyGroupList(){
        List<FamilyGroupVo> list = familyGroupService.getRecommendFamilyGroupList();
        return Result.success(list);
    }

    /**
     * 获取最新通过审核的家庭组列表
     * @return 最新通过审核的家庭组列表
     */
    @Operation(summary = "获取最新通过审核的家庭组列表（最新家庭组）", description = "按审核时间倒序排序，返回最新通过审核的家庭组")
    @GetMapping("/getLatestReviewedFamilyGroupList")
    public Result<List<FamilyGroupVo>> getLatestReviewedFamilyGroupList(){
        List<FamilyGroupVo> list = familyGroupService.getLatestReviewedFamilyGroupList();
        return Result.success(list);
    }

}
