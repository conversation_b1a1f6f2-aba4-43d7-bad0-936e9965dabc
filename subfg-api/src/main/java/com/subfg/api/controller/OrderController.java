package com.subfg.api.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v3/order")
@Tag(name = "订单管理", description = "订单管理")
public class OrderController {

    // /**
    //  * 查询当前用户订单分页列表
    //  */
    // @GetMapping("/page")
    // @Operation(summary = "查询当前用户订单分页列表", description = "查询当前用户订单分页列表")
    // public Result<Page<OrderPo>> page(@Valid @ModelAttribute OrderPageReq req){
    //     return Result.success(orderService.page(req));
    // }

}
