package com.subfg.api.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.api.Serivce.FamilyGroupLeaderService;
import com.subfg.domain.request.FamilyGroupMemberActivateReq;
import com.subfg.domain.request.FamilyGroupMemberRemoveReq;
import com.subfg.domain.request.GroupPageReq;
import com.subfg.domain.request.LaunchGroupReq;
import com.subfg.domain.request.UpdateFamilyGroupSettingsReq;
import com.subfg.domain.vo.LeaderFamilyGroupVo;
import com.subfg.domain.vo.Result;
import com.subfg.domain.vo.UserFamilyGroupDetailVo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 家庭组团长管理控制器
 * 提供团长专用的管理功能，包括成员管理、权限转移、家庭组设置等
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/familyGroup/leader")
@Tag(name = "团长家庭组管理", description = "团长专用的家庭组管理功能")
public class FamilyGroupLeaderController {

    private final FamilyGroupLeaderService familyGroupLeaderService;

    /**
     * 获取团长管理的家庭组列表（包含成员激活状态统计）
     */
    @GetMapping("/groups")
    @Operation(summary = "获取团长管理的家庭组列表(领航车队)", description = "获取当前用户作为团长管理的所有家庭组，包含成员激活状态统计")
    public Result<Page<LeaderFamilyGroupVo>> getLeaderGroups(@Valid @ModelAttribute GroupPageReq req) {
        Page<LeaderFamilyGroupVo> groups = familyGroupLeaderService.getLeaderGroups(req);
        return Result.success(groups);
    }

    /**
     * 获取家庭组详情信息(团长视角)
     * @param familyGroupId 家庭组ID
     * @return
     */
    @GetMapping("/getFamilyGroupDetail")
    @Operation(summary = "获取家庭组详情信息(团长视角)", description = "获取当前用户作为团长管理的家庭组详情信息")
    public Result<UserFamilyGroupDetailVo> getGroupDetail(
            @Parameter(description = "家庭组ID") @RequestParam String familyGroupId) {
        UserFamilyGroupDetailVo detail = familyGroupLeaderService.getFamilyGroupDetail(familyGroupId);
        return Result.success(detail);
    }

    /**
     * 移除家庭组成员
     */
    @PostMapping("/members/remove")
    @Operation(summary = "移除家庭组成员", description = "团长移除指定成员出家庭组")
    public Result<Void> removeMember(@Valid @RequestBody FamilyGroupMemberRemoveReq req) {
        familyGroupLeaderService.removeMember(req);
        return Result.success();
    }

    /**
     * 激活待激活成员
     */
    @PostMapping("/members/activate")
    @Operation(summary = "激活成员", description = "团长激活待激活状态的成员")
    public Result<Void> activateMember(@Valid @RequestBody FamilyGroupMemberActivateReq req) {
        familyGroupLeaderService.activateMember(req);
        return Result.success();
    }

    /**
     * 修改家庭组设置
     */
    @PostMapping("/groups/settings")
    @Operation(summary = "修改家庭组设置(完善审核信息)", description = "团长完善审核信息")
    public Result<Void> updateGroupSettings(@Valid @RequestBody UpdateFamilyGroupSettingsReq req) {
        familyGroupLeaderService.updateGroupSettings(req);
        return Result.success();
    }

    /**
     * 发车操作（拼团家庭组）
     */
    @PostMapping("/groups/launch")
    @Operation(summary = "发车操作", description = "团长手动发车，将拼团家庭组转为待审核状态")
    public Result<Void> launchGroup(@RequestBody LaunchGroupReq req) {
        familyGroupLeaderService.launchGroup(req);
        return Result.success();
    }

    /**
     * 关闭家庭组
     */
    @PostMapping("/groups/close")
    @Operation(summary = "关闭家庭组", description = "团长关闭家庭组")
    public Result<Void> closeGroup(@RequestParam String familyGroupId) {
        familyGroupLeaderService.closeGroup(familyGroupId);
        return Result.success();
    }

}
