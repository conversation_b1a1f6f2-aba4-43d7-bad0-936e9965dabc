package com.subfg.api.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.api.Serivce.UserService;
import com.subfg.domain.entity.user.UserPo;
import com.subfg.domain.request.BindThirdPartyAccountReq;
import com.subfg.domain.request.ChangeEmailReq;
import com.subfg.domain.request.ChangePasswordReq;
import com.subfg.domain.request.ForgetPasswordReq;
import com.subfg.domain.request.UnbindThirdPartyAccountReq;
import com.subfg.domain.request.UpdateUserInfoReq;
import com.subfg.domain.vo.Result;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/user")
@Tag(name = "用户管理", description = "用户管理相关接口")
public class UserController {

    private final UserService userService;


    /**
     * 获取用户个人信息
     */
    @GetMapping("/getUserInfo")
    @Operation(summary = "获取用户个人信息", description = "获取用户个人信息")
    public Result<UserPo> getUserInfo(){
        return Result.success(userService.getUserInfo());
    }
    
    /**
     * 用户修改个人信息
     */
    @PostMapping("/updateUserInfo")
    @Operation(summary = "用户修改个人信息", description = "用户修改个人信息")
    public Result<String> updateUserInfo(@Valid @RequestBody UpdateUserInfoReq req){
        userService.updateUserInfo(req);
        return Result.success();
    }

    /**
     * 用户绑定三方账号 目前系统只支持微信
     */
    @PostMapping("/bindThirdPartyAccount")
    @Operation(summary = "用户绑定三方账号", description = "用户绑定三方账号")
    public Result<String> bindThirdPartyAccount(@Valid @RequestBody BindThirdPartyAccountReq req){
        userService.bindThirdPartyAccount(req);
        return Result.success();
    }

    /**
     * 用户解绑三方账号
     */
    @PostMapping("/unbindThirdPartyAccount")
    @Operation(summary = "用户解绑三方账号", description = "用户解绑三方账号")
    public Result<String> unbindThirdPartyAccount(@Valid @RequestBody UnbindThirdPartyAccountReq req){
        userService.unbindThirdPartyAccount(req);
        return Result.success();
    }

    /**
     * 用户换绑邮箱
     */
    @PostMapping("/changeEmail")
    @Operation(summary = "用户换绑邮箱", description = "用户换绑邮箱")
    public Result<String> changeEmail(@Valid @RequestBody ChangeEmailReq req){
        userService.changeEmail(req);
        return Result.success();
    }

    /**
     * 用户修改密码
     */
    @PostMapping("/changePassword")
    @Operation(summary = "用户修改密码", description = "用户修改密码，支持验证旧密码修改和邮箱验证码修改两种方式")
    public Result<String> changePassword(@Valid @RequestBody ChangePasswordReq req){
        userService.changePassword(req);
        return Result.success();
    }

    /**
     * 忘记密码
     */
    @PostMapping("/forgetPassword")
    @Operation(summary = "忘记密码(重置密码)", description = "忘记密码(重置密码)")
    public Result<String> forgetPassword(@Valid @RequestBody ForgetPasswordReq req){
        userService.forgetPassword(req);
        return Result.success();
    }

}
