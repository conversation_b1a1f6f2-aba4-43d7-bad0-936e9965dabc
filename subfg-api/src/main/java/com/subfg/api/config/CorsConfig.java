package com.subfg.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import lombok.extern.slf4j.Slf4j;

/**
 * 跨域配置类
 * 配置CORS（Cross-Origin Resource Sharing）以支持前端跨域请求
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    /**
     * 配置跨域映射
     * 通过 WebMvcConfigurer 接口配置跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        log.info("配置跨域映射...");
        
        registry.addMapping("/**")
                // 允许所有源域名
                .allowedOriginPatterns("*")
                // 允许的请求方法
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
                // 允许的请求头
                .allowedHeaders("*")
                // 是否允许发送Cookie
                .allowCredentials(true)
                // 预检请求的缓存时间（秒）
                .maxAge(3600);
                
        log.info("跨域配置完成");
    }

    /**
     * 配置CORS配置源
     * 提供更细粒度的跨域配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        log.info("配置CORS配置源...");
        
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许所有源域名
        configuration.addAllowedOriginPattern("*");
        
        // 允许的请求方法
        configuration.addAllowedMethod("GET");
        configuration.addAllowedMethod("POST");
        configuration.addAllowedMethod("PUT");
        configuration.addAllowedMethod("DELETE");
        configuration.addAllowedMethod("OPTIONS");
        configuration.addAllowedMethod("PATCH");
        configuration.addAllowedMethod("HEAD");
        
        // 允许的请求头
        configuration.addAllowedHeader("*");
        
        // 允许发送认证信息（如Cookie、Authorization头等）
        configuration.setAllowCredentials(true);
        
        // 预检请求的缓存时间
        configuration.setMaxAge(3600L);
        
        // 暴露的响应头（前端可以访问的响应头）
        configuration.addExposedHeader("Authorization");
        configuration.addExposedHeader("Content-Type");
        configuration.addExposedHeader("X-Total-Count");
        configuration.addExposedHeader("X-Request-Id");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        log.info("CORS配置源配置完成");
        return source;
    }
}
