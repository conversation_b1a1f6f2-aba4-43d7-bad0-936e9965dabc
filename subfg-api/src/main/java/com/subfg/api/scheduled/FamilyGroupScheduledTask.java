package com.subfg.api.scheduled;

import java.util.List;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.repository.mapper.FgFamilyGroupMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 家庭组定时任务服务类
 * 处理拼团家庭组的定时检查和状态更新
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGroupScheduledTask {

    private final FgFamilyGroupMapper familyGroupMapper;

    /**
     * 每天0点扫描拼团家庭组，检查截止时间并更新状态
     * 使用cron表达式：0 0 0 * * ? 表示每天0点0分0秒执行
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @Async("ioTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void checkGroupBuyingDeadlineAndUpdateStatus() {
        log.info("开始执行拼团家庭组截止时间检查任务 - 执行线程: {}", Thread.currentThread().getName());
        
        try {
            Long currentTime = TimeUtil.getCurrentTimestamp();
            log.info("当前时间戳: {}, 当前时间: {}", currentTime, TimeUtil.timestampToDateTime(currentTime));
            
            // 查询所有状态为"组建中"的拼团家庭组
            List<FgFamilyGroupPo> groupBuyingGroups = queryBuildingGroupBuyingGroups();
            
            if (groupBuyingGroups.isEmpty()) {
                log.info("没有找到状态为组建中的拼团家庭组，任务结束");
                return;
            }
            

            
            // 遍历检查每个拼团家庭组的截止时间
            for (FgFamilyGroupPo group : groupBuyingGroups) {
                try {
                    if (isGroupExpired(group, currentTime)) {
                        // 截止时间已到，更新状态为解散（关闭）
                        updateGroupStatusToClosed(group);
                        
                        log.info("拼团家庭组已解散 - ID: {}, 名称: {}, 截止时间: {}, 当前成员数: {}/{}", 
                            group.getFamilyGroupId(), 
                            group.getFamilyGroupName(),
                            TimeUtil.timestampToDateTime(group.getDeadline()),
                            group.getCurrentMemberCount(),
                            group.getSumVacancy());
                    } else {
                        log.debug("拼团家庭组未到截止时间 - ID: {}, 名称: {}, 截止时间: {}", 
                            group.getFamilyGroupId(), 
                            group.getFamilyGroupName(),
                            TimeUtil.timestampToDateTime(group.getDeadline()));
                    }
                } catch (Exception e) {
                    log.error("处理拼团家庭组时发生异常 - ID: {}, 名称: {}, 错误: {}", 
                        group.getFamilyGroupId(), 
                        group.getFamilyGroupName(), 
                        e.getMessage(), e);
                    // 继续处理下一个，不因单个失败而中断整个任务
                }
            }
            

                
        } catch (Exception e) {
            log.error("执行拼团家庭组截止时间检查任务时发生异常: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让Spring的事务管理器处理回滚
        }
    }

    /**
     * 查询所有状态为"组建中"的拼团家庭组
     */
    private List<FgFamilyGroupPo> queryBuildingGroupBuyingGroups() {
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getGroupType, FamilyGroupConstants.GroupType.GROUP_BUYING)
                   .eq(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.BUILDING)
                   .isNotNull(FgFamilyGroupPo::getDeadline)
                   .orderByAsc(FgFamilyGroupPo::getDeadline); // 按截止时间升序排列
        
        return familyGroupMapper.selectList(queryWrapper);
    }

    /**
     * 判断拼团家庭组是否已过期
     */
    private boolean isGroupExpired(FgFamilyGroupPo group, Long currentTime) {
        if (group.getDeadline() == null) {
            log.warn("拼团家庭组截止时间为空 - ID: {}, 名称: {}", 
                group.getFamilyGroupId(), group.getFamilyGroupName());
            return false;
        }
        
        return group.getDeadline() <= currentTime;
    }

    /**
     * 更新拼团家庭组状态为关闭（解散）
     */
    private void updateGroupStatusToClosed(FgFamilyGroupPo group) {
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, group.getFamilyGroupId())
                    .eq(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.BUILDING) // 确保状态仍为组建中
                    .set(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.CLOSED)
                    .set(FgFamilyGroupPo::getUpdateTime, currentTime);
        
        int updateCount = familyGroupMapper.update(null, updateWrapper);
        
        if (updateCount > 0) {
            log.info("成功更新拼团家庭组状态为解散 - ID: {}, 名称: {}", 
                group.getFamilyGroupId(), group.getFamilyGroupName());
        } else {
            log.warn("更新拼团家庭组状态失败，可能已被其他操作修改 - ID: {}, 名称: {}", 
                group.getFamilyGroupId(), group.getFamilyGroupName(), updateCount);
        }
    }

    /**
     * 手动触发拼团家庭组截止时间检查（用于测试）
     * 注意：此方法仅用于开发和测试环境，生产环境应移除或添加权限控制
     */
    @Async("ioTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void manualCheckGroupBuyingDeadline() {
        log.info("手动触发拼团家庭组截止时间检查任务");
        checkGroupBuyingDeadlineAndUpdateStatus();
    }

    /**
     * 查询即将到期的拼团家庭组（提前通知功能，可选实现）
     * 
     * @param hoursBeforeDeadline 截止时间前多少小时
     * @return 即将到期的拼团家庭组列表
     */
    public List<FgFamilyGroupPo> getGroupsNearDeadline(int hoursBeforeDeadline) {
        Long currentTime = TimeUtil.getCurrentTimestamp();
        Long thresholdTime = currentTime + (hoursBeforeDeadline * 60 * 60L); // 秒级时间戳，不需要乘以1000
        
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getGroupType, FamilyGroupConstants.GroupType.GROUP_BUYING)
                   .eq(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.BUILDING)
                   .isNotNull(FgFamilyGroupPo::getDeadline)
                   .le(FgFamilyGroupPo::getDeadline, thresholdTime)
                   .gt(FgFamilyGroupPo::getDeadline, currentTime)
                   .orderByAsc(FgFamilyGroupPo::getDeadline);
        
        return familyGroupMapper.selectList(queryWrapper);
    }
}
