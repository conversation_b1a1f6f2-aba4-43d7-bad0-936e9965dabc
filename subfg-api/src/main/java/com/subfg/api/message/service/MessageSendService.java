package com.subfg.api.message.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.subfg.api.message.factory.MessageStrategyFactory;
import com.subfg.api.message.strategy.MessageStrategy;
import com.subfg.common.constans.MessageConstants;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息发送服务
 * 统一的消息发送入口，使用策略模式处理不同业务类型的消息
 */
@Slf4j
@Service
public class MessageSendService {

    @Autowired
    private MessageStrategyFactory messageStrategyFactory;

    // ==================== 简化发送方法 ====================

    /**
     * 发送消息（最简化版本，自动提取变量）
     */
    public void sendMessage(String userId, String businessType, String sceneType, String businessId) {
        try {
            MessageStrategy strategy = messageStrategyFactory.getStrategy(businessType);
            strategy.sendMessage(userId, sceneType, businessId);
        } catch (Exception e) {
            log.error("消息发送失败 - userId: {}, businessType: {}, sceneType: {}",
                     userId, businessType, sceneType, e);
        }
    }

    // ==================== 家庭组消息便捷方法 ====================

    /**
     * 发送家庭组邀请消息
     */
    public void sendFamilyGroupInvite(String userId, String groupId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_FAMILY_GROUP,
                   MessageConstants.FamilyGroup.INVITE, groupId);
    }

    /**
     * 发送家庭组审批通过消息
     */
    public void sendFamilyGroupApprove(String userId, String groupId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_FAMILY_GROUP,
                   MessageConstants.FamilyGroup.APPROVE, groupId);
    }

    /**
     * 发送家庭组审批拒绝消息
     */
    public void sendFamilyGroupReject(String userId, String groupId, String reason) {
        Map<String, Object> contextData = Map.of("reason", reason);
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_FAMILY_GROUP,
                   MessageConstants.FamilyGroup.REJECT, groupId, contextData);
    }

    /**
     * 发送家庭组成员加入消息
     */
    public void sendFamilyGroupJoin(String userId, String groupId, String memberName) {
        Map<String, Object> contextData = Map.of("memberName", memberName);
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_FAMILY_GROUP,
                   MessageConstants.FamilyGroup.JOIN, groupId, contextData);
    }

    /**
     * 发送家庭组解散消息
     */
    public void sendFamilyGroupDissolve(String userId, String groupId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_FAMILY_GROUP,
                   MessageConstants.FamilyGroup.DISSOLVE, groupId);
    }

    // ==================== 订单消息便捷方法 ====================

    /**
     * 发送订单创建消息
     */
    public void sendOrderCreate(String userId, String orderId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_ORDER,
                   MessageConstants.Order.CREATE, orderId);
    }

    /**
     * 发送订单完成消息
     */
    public void sendOrderComplete(String userId, String orderId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_ORDER,
                   MessageConstants.Order.COMPLETE, orderId);
    }

    /**
     * 发送订单取消消息
     */
    public void sendOrderCancel(String userId, String orderId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_ORDER,
                   MessageConstants.Order.CANCEL, orderId);
    }

    // ==================== 支付消息便捷方法 ====================

    /**
     * 发送支付成功消息
     */
    public void sendPaymentSuccess(String userId, String paymentId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_PAYMENT,
                   MessageConstants.Payment.SUCCESS, paymentId);
    }

    /**
     * 发送支付失败消息
     */
    public void sendPaymentFailed(String userId, String paymentId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_PAYMENT,
                   MessageConstants.Payment.FAILED, paymentId);
    }

    // ==================== 用户消息便捷方法 ====================

    /**
     * 发送用户注册欢迎消息
     */
    public void sendUserWelcome(String userId) {
        sendMessage(userId, MessageConstants.BUSINESS_TYPE_USER,
                   MessageConstants.User.REGISTER, userId);
    }

    // ==================== 原有方法 ====================

    /**
     * 发送消息（带上下文数据）
     *
     * @param userId 用户ID
     * @param businessType 业务类型
     * @param sceneType 场景类型
     * @param businessId 业务ID
     * @param contextData 上下文数据
     */
    public void sendMessage(String userId, String businessType, String sceneType,
                          String businessId, Map<String, Object> contextData) {
        try {
            // 1. 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                log.error("用户ID不能为空");
                return;
            }

            if (businessType == null || businessType.trim().isEmpty()) {
                log.error("业务类型不能为空");
                return;
            }

            if (sceneType == null || sceneType.trim().isEmpty()) {
                log.error("场景类型不能为空");
                return;
            }

            // 2. 获取对应的消息策略
            MessageStrategy strategy = messageStrategyFactory.getStrategy(businessType);

            // 3. 发送消息
            strategy.sendMessage(userId, sceneType, businessId, contextData);

            log.info("消息发送请求处理完成 - userId: {}, businessType: {}, sceneType: {}", 
                    userId, businessType, sceneType);

        } catch (Exception e) {
            log.error("消息发送失败 - userId: {}, businessType: {}, sceneType: {}", 
                     userId, businessType, sceneType, e);
        }
    }

    /**
     * 发送消息（支持自定义优先级）
     *
     * @param userId 用户ID
     * @param businessType 业务类型
     * @param sceneType 场景类型
     * @param businessId 业务ID
     * @param variables 模板变量
     * @param priority 优先级
     */
    public void sendMessage(String userId, String businessType, String sceneType, 
                          String businessId, Map<String, Object> variables, Integer priority) {
        try {
            // 1. 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                log.error("用户ID不能为空");
                return;
            }

            if (businessType == null || businessType.trim().isEmpty()) {
                log.error("业务类型不能为空");
                return;
            }

            if (sceneType == null || sceneType.trim().isEmpty()) {
                log.error("场景类型不能为空");
                return;
            }

            // 2. 获取对应的消息策略
            MessageStrategy strategy = messageStrategyFactory.getStrategy(businessType);

            // 3. 发送消息
            strategy.sendMessage(userId, sceneType, businessId, variables, priority);

            log.info("消息发送请求处理完成 - userId: {}, businessType: {}, sceneType: {}, priority: {}", 
                    userId, businessType, sceneType, priority);

        } catch (Exception e) {
            log.error("消息发送失败 - userId: {}, businessType: {}, sceneType: {}", 
                     userId, businessType, sceneType, e);
        }
    }

    /**
     * 批量发送消息
     *
     * @param userIds 用户ID列表
     * @param businessType 业务类型
     * @param sceneType 场景类型
     * @param businessId 业务ID
     * @param variables 模板变量
     */
    public void sendBatchMessage(List<String> userIds, String businessType, String sceneType, 
                               String businessId, Map<String, Object> variables) {
        if (userIds == null || userIds.isEmpty()) {
            log.error("用户ID列表不能为空");
            return;
        }

        for (String userId : userIds) {
            sendMessage(userId, businessType, sceneType, businessId, variables);
        }

        log.info("批量消息发送完成 - userCount: {}, businessType: {}, sceneType: {}", 
                userIds.size(), businessType, sceneType);
    }

    /**
     * 检查是否支持指定的业务类型和场景类型
     *
     * @param businessType 业务类型
     * @param sceneType 场景类型
     * @return 是否支持
     */
    public boolean isSupported(String businessType, String sceneType) {
        return messageStrategyFactory.isSupported(businessType, sceneType);
    }

    /**
     * 获取所有支持的业务类型
     *
     * @return 业务类型列表
     */
    public Set<String> getSupportedBusinessTypes() {
        return messageStrategyFactory.getSupportedBusinessTypes();
    }
}
