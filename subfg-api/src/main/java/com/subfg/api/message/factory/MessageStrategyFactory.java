package com.subfg.api.message.factory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.MessageStrategy;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息策略工厂
 * 负责根据业务类型获取对应的消息策略
 */
@Slf4j
@Component
public class MessageStrategyFactory {

    @Autowired
    private List<MessageStrategy> messageStrategies;

    private final Map<String, MessageStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (MessageStrategy strategy : messageStrategies) {
            String businessType = strategy.getSupportedBusinessType();
            strategyMap.put(businessType, strategy);
            log.info("注册消息策略 - businessType: {}, strategy: {}", 
                    businessType, strategy.getClass().getSimpleName());
        }
        log.info("消息策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());
    }

    /**
     * 根据业务类型获取消息策略
     *
     * @param businessType 业务类型
     * @return 消息策略
     */
    public MessageStrategy getStrategy(String businessType) {
        MessageStrategy strategy = strategyMap.get(businessType);
        if (strategy == null) {
            log.error("未找到对应的消息策略 - businessType: {}", businessType);
            throw new IllegalArgumentException("不支持的业务类型: " + businessType);
        }
        return strategy;
    }

    /**
     * 检查是否支持指定的业务类型
     *
     * @param businessType 业务类型
     * @return 是否支持
     */
    public boolean isSupported(String businessType) {
        return strategyMap.containsKey(businessType);
    }

    /**
     * 检查是否支持指定的业务类型和场景类型
     *
     * @param businessType 业务类型
     * @param sceneType 场景类型
     * @return 是否支持
     */
    public boolean isSupported(String businessType, String sceneType) {
        MessageStrategy strategy = strategyMap.get(businessType);
        if (strategy == null) {
            return false;
        }
        return strategy.isSupportedScene(sceneType);
    }

    /**
     * 获取所有支持的业务类型
     *
     * @return 业务类型列表
     */
    public java.util.Set<String> getSupportedBusinessTypes() {
        return strategyMap.keySet();
    }
}
