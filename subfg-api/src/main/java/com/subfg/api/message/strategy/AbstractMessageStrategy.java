package com.subfg.api.message.strategy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;

import com.subfg.api.mq.MessageProducerService;
import com.subfg.common.constans.MessageConstants;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.message.MessageTemplatePo;
import com.subfg.repository.mapper.MessageTemplateMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象消息策略类
 * 提供通用的消息发送逻辑
 */
@Slf4j
public abstract class AbstractMessageStrategy implements MessageStrategy {

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    @Autowired
    protected MessageTemplateMapper messageTemplateMapper;

    @Autowired
    protected MessageProducerService messageProducerService;

    /**
     * 获取支持的业务类型
     */
    public abstract String getSupportedBusinessType();

    /**
     * 验证场景类型是否支持
     */
    public abstract boolean isSupportedScene(String sceneType);

    @Override
    public void sendMessage(String userId, String sceneType, String businessId) {
        sendMessage(userId, sceneType, businessId, null, getDefaultPriority(sceneType));
    }

    @Override
    public void sendMessage(String userId, String sceneType, String businessId, Map<String, Object> contextData) {
        sendMessage(userId, sceneType, businessId, contextData, getDefaultPriority(sceneType));
    }

    @Override
    public void sendMessage(String userId, String sceneType, String businessId, Map<String, Object> contextData, Integer priority) {
        try {
            // 1. 验证场景类型
            if (!isSupportedScene(sceneType)) {
                log.error("不支持的场景类型 - businessType: {}, sceneType: {}", getSupportedBusinessType(), sceneType);
                return;
            }

            // 2. 查找消息模板
            MessageTemplatePo template = messageTemplateMapper.selectByBusinessAndScene(
                getSupportedBusinessType(), sceneType);

            if (template == null) {
                log.error("未找到消息模板 - businessType: {}, sceneType: {}", 
                         getSupportedBusinessType(), sceneType);
                return;
            }

            if (!template.getEnable()) {
                log.warn("消息模板已禁用 - templateId: {}", template.getTemplateId());
                return;
            }

            // 3. 自动提取变量
            Map<String, Object> finalVariables = extractVariables(businessId, sceneType, template.getRequiredVariables(), contextData != null ? contextData : new HashMap<>());

            // 4. 渲染模板内容
            String title = renderTemplate(template.getTitleTemplate(), finalVariables);
            String content = renderTemplate(template.getContentTemplate(), finalVariables);

            // 4. 构建消息
            MessageProducerService.UserNotificationMessage message = 
                MessageProducerService.UserNotificationMessage.builder()
                    .userId(userId)
                    .title(title)
                    .content(content)
                    .type(template.getMessageType())
                    .businessType(getSupportedBusinessType())
                    .sceneType(sceneType)
                    .businessId(businessId)
                    .priority(priority)
                    .extraData(finalVariables)
                    .timestamp(TimeUtil.getCurrentTimestamp())
                    .build();

            // 5. 发送消息
            messageProducerService.sendUserNotification(message.getUserId(), message.getTitle(), message.getContent(), message.getType());

            log.info("消息发送成功 - userId: {}, businessType: {}, sceneType: {}", 
                    userId, getSupportedBusinessType(), sceneType);

        } catch (Exception e) {
            log.error("消息发送失败 - userId: {}, businessType: {}, sceneType: {}", 
                     userId, getSupportedBusinessType(), sceneType, e);
        }
    }

    /**
     * 自动提取变量
     * 根据模板定义的所需变量，从业务数据中提取对应的值
     */
    protected Map<String, Object> extractVariables(String businessId, String sceneType,
                                                  List<String> requiredVariables, Map<String, Object> contextData) {
        Map<String, Object> variables = new HashMap<>();

        // 添加上下文数据
        if (contextData != null) {
            variables.putAll(contextData);
        }

        // 如果没有定义所需变量，直接返回上下文数据
        if (requiredVariables == null || requiredVariables.isEmpty()) {
            return variables;
        }

        // 根据业务类型提取数据
        Map<String, Object> businessData = extractBusinessData(businessId, sceneType);
        if (businessData != null) {
            variables.putAll(businessData);
        }

        // 只保留模板需要的变量
        Map<String, Object> filteredVariables = new HashMap<>();
        for (String varName : requiredVariables) {
            if (variables.containsKey(varName)) {
                filteredVariables.put(varName, variables.get(varName));
            } else {
                log.warn("缺少必需的变量: {} - businessType: {}, sceneType: {}",
                        varName, getSupportedBusinessType(), sceneType);
                filteredVariables.put(varName, ""); // 设置默认值
            }
        }

        return filteredVariables;
    }

    /**
     * 提取业务数据
     * 子类需要实现此方法来获取具体的业务数据
     */
    protected abstract Map<String, Object> extractBusinessData(String businessId, String sceneType);

    /**
     * 渲染模板内容
     */
    protected String renderTemplate(String template, Map<String, Object> variables) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        if (variables == null || variables.isEmpty()) {
            return template;
        }

        String result = template;
        Matcher matcher = VARIABLE_PATTERN.matcher(template);

        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            
            if (value != null) {
                String replacement = value.toString();
                result = result.replace("${" + variableName + "}", replacement);
            } else {
                log.warn("模板变量未找到对应值: {}", variableName);
                result = result.replace("${" + variableName + "}", "");
            }
        }

        return result;
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 子类可以重写此方法来定义不同场景的默认优先级
        return MessageConstants.PRIORITY_MEDIUM;
    }

    /**
     * 安全获取字符串值
     */
    protected String safeGetString(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取字符串值（带默认值）
     */
    protected String safeGetString(Object value, String defaultValue) {
        return value != null ? value.toString() : defaultValue;
    }
}
