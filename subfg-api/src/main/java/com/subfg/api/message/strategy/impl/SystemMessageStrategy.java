package com.subfg.api.message.strategy.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.AbstractMessageStrategy;
import com.subfg.common.constans.MessageConstants;

/**
 * 系统消息策略
 */
@Component
public class SystemMessageStrategy extends AbstractMessageStrategy {

    // 支持的场景类型
    private static final List<String> SUPPORTED_SCENES = Arrays.asList(
        MessageConstants.System.MAINTAIN,
        MessageConstants.System.UPGRADE,
        MessageConstants.System.FEATURE_ONLINE,
        MessageConstants.System.FEATURE_OFFLINE,
        MessageConstants.System.SECURITY_ALERT,
        MessageConstants.System.ANNOUNCEMENT,
        MessageConstants.System.ACTIVITY
    );

    @Override
    public String getSupportedBusinessType() {
        return MessageConstants.BUSINESS_TYPE_SYSTEM;
    }

    @Override
    public boolean isSupportedScene(String sceneType) {
        return SUPPORTED_SCENES.contains(sceneType);
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 根据不同场景设置不同的默认优先级
        switch (sceneType) {
            case MessageConstants.System.SECURITY_ALERT:
                return MessageConstants.PRIORITY_URGENT; // 安全提醒优先级最高
            
            case MessageConstants.System.MAINTAIN:
            case MessageConstants.System.UPGRADE:
                return MessageConstants.PRIORITY_HIGH; // 维护和升级优先级较高
            
            case MessageConstants.System.FEATURE_OFFLINE:
                return MessageConstants.PRIORITY_HIGH; // 功能下线优先级较高
            
            case MessageConstants.System.ANNOUNCEMENT:
                return MessageConstants.PRIORITY_MEDIUM; // 公告通知中等优先级
            
            case MessageConstants.System.FEATURE_ONLINE:
            case MessageConstants.System.ACTIVITY:
                return MessageConstants.PRIORITY_LOW; // 功能上线和活动优先级较低
            
            default:
                return MessageConstants.PRIORITY_MEDIUM;
        }
    }

    @Override
    protected Map<String, Object> extractBusinessData(String businessId, String sceneType) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'extractBusinessData'");
    }
}
