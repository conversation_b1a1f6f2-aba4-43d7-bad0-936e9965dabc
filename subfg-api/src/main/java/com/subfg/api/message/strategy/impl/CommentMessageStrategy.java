package com.subfg.api.message.strategy.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.AbstractMessageStrategy;
import com.subfg.common.constans.MessageConstants;

/**
 * 评论消息策略
 */
@Component
public class CommentMessageStrategy extends AbstractMessageStrategy {

    // 支持的场景类型
    private static final List<String> SUPPORTED_SCENES = Arrays.asList(
        MessageConstants.Comment.CREATE,
        MessageConstants.Comment.REPLY,
        MessageConstants.Comment.LIKE,
        MessageConstants.Comment.DELETE,
        MessageConstants.Comment.REPORT,
        MessageConstants.Comment.APPROVE,
        MessageConstants.Comment.REJECT
    );

    @Override
    public String getSupportedBusinessType() {
        return MessageConstants.BUSINESS_TYPE_COMMENT;
    }

    @Override
    public boolean isSupportedScene(String sceneType) {
        return SUPPORTED_SCENES.contains(sceneType);
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 根据不同场景设置不同的默认优先级
        switch (sceneType) {
            case MessageConstants.Comment.REPLY:
                return MessageConstants.PRIORITY_MEDIUM; // 评论回复中等优先级
            
            case MessageConstants.Comment.DELETE:
            case MessageConstants.Comment.REPORT:
                return MessageConstants.PRIORITY_HIGH; // 删除和举报优先级较高
            
            case MessageConstants.Comment.APPROVE:
            case MessageConstants.Comment.REJECT:
                return MessageConstants.PRIORITY_MEDIUM; // 审核结果中等优先级
            
            case MessageConstants.Comment.CREATE:
            case MessageConstants.Comment.LIKE:
                return MessageConstants.PRIORITY_LOW; // 新评论和点赞优先级较低
            
            default:
                return MessageConstants.PRIORITY_MEDIUM;
        }
    }

    @Override
    protected Map<String, Object> extractBusinessData(String businessId, String sceneType) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'extractBusinessData'");
    }
}
