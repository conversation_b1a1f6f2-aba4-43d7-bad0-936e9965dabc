package com.subfg.api.message.strategy.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.AbstractMessageStrategy;
import com.subfg.common.constans.MessageConstants;

/**
 * 订单消息策略
 */
@Component
public class OrderMessageStrategy extends AbstractMessageStrategy {

    // 支持的场景类型
    private static final List<String> SUPPORTED_SCENES = Arrays.asList(
        MessageConstants.Order.CREATE,
        MessageConstants.Order.CONFIRM,
        MessageConstants.Order.PAYMENT,
        MessageConstants.Order.SHIP,
        MessageConstants.Order.COMPLETE,
        MessageConstants.Order.CANCEL,
        MessageConstants.Order.REFUND,
        MessageConstants.Order.TIMEOUT,
        MessageConstants.Order.EXCEPTION
    );

    @Override
    public String getSupportedBusinessType() {
        return MessageConstants.BUSINESS_TYPE_ORDER;
    }

    @Override
    public boolean isSupportedScene(String sceneType) {
        return SUPPORTED_SCENES.contains(sceneType);
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 根据不同场景设置不同的默认优先级
        switch (sceneType) {
            case MessageConstants.Order.CREATE:
                return MessageConstants.PRIORITY_HIGH; // 订单创建优先级较高
            
            case MessageConstants.Order.PAYMENT:
                return MessageConstants.PRIORITY_HIGH; // 支付相关优先级较高
            
            case MessageConstants.Order.TIMEOUT:
            case MessageConstants.Order.EXCEPTION:
                return MessageConstants.PRIORITY_URGENT; // 超时和异常优先级最高
            
            case MessageConstants.Order.COMPLETE:
                return MessageConstants.PRIORITY_MEDIUM; // 完成中等优先级
            
            case MessageConstants.Order.CONFIRM:
            case MessageConstants.Order.SHIP:
                return MessageConstants.PRIORITY_MEDIUM; // 确认和发货中等优先级
            
            case MessageConstants.Order.CANCEL:
            case MessageConstants.Order.REFUND:
                return MessageConstants.PRIORITY_LOW; // 取消和退款优先级较低
            
            default:
                return MessageConstants.PRIORITY_MEDIUM;
        }
    }

    @Override
    protected Map<String, Object> extractBusinessData(String businessId, String sceneType) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'extractBusinessData'");
    }
}
