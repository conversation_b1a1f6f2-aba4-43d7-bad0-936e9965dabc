package com.subfg.api.message.strategy.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.AbstractMessageStrategy;
import com.subfg.common.constans.MessageConstants;

/**
 * 支付消息策略
 */
@Component
public class PaymentMessageStrategy extends AbstractMessageStrategy {

    // 支持的场景类型
    private static final List<String> SUPPORTED_SCENES = Arrays.asList(
        MessageConstants.Payment.SUCCESS,
        MessageConstants.Payment.FAILED,
        MessageConstants.Payment.TIMEOUT,
        MessageConstants.Payment.CANCEL,
        MessageConstants.Payment.REFUND_SUCCESS,
        MessageConstants.Payment.REFUND_FAILED,
        MessageConstants.Payment.REFUND_PROCESSING
    );

    @Override
    public String getSupportedBusinessType() {
        return MessageConstants.BUSINESS_TYPE_PAYMENT;
    }

    @Override
    public boolean isSupportedScene(String sceneType) {
        return SUPPORTED_SCENES.contains(sceneType);
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 根据不同场景设置不同的默认优先级
        switch (sceneType) {
            case MessageConstants.Payment.SUCCESS:
                return MessageConstants.PRIORITY_HIGH; // 支付成功优先级较高
            
            case MessageConstants.Payment.FAILED:
            case MessageConstants.Payment.TIMEOUT:
                return MessageConstants.PRIORITY_URGENT; // 支付失败和超时优先级最高
            
            case MessageConstants.Payment.REFUND_SUCCESS:
                return MessageConstants.PRIORITY_HIGH; // 退款成功优先级较高
            
            case MessageConstants.Payment.REFUND_FAILED:
                return MessageConstants.PRIORITY_URGENT; // 退款失败优先级最高
            
            case MessageConstants.Payment.REFUND_PROCESSING:
                return MessageConstants.PRIORITY_MEDIUM; // 退款处理中中等优先级
            
            case MessageConstants.Payment.CANCEL:
                return MessageConstants.PRIORITY_LOW; // 支付取消优先级较低
            
            default:
                return MessageConstants.PRIORITY_MEDIUM;
        }
    }

    @Override
    protected Map<String, Object> extractBusinessData(String businessId, String sceneType) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'extractBusinessData'");
    }
}
