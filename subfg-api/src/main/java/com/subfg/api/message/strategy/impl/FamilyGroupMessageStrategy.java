package com.subfg.api.message.strategy.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.AbstractMessageStrategy;
import com.subfg.common.constans.MessageConstants;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.entity.user.UserPo;
import com.subfg.repository.mapper.FgFamilyGroupMapper;
import com.subfg.repository.mapper.UserMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 家庭组消息策略
 */
@Slf4j
@Component
public class FamilyGroupMessageStrategy extends AbstractMessageStrategy {

    @Autowired
    private FgFamilyGroupMapper familyGroupMapper;

    @Autowired
    private UserMapper userMapper;

    // 支持的场景类型
    private static final List<String> SUPPORTED_SCENES = Arrays.asList(
        MessageConstants.FamilyGroup.INVITE,
        MessageConstants.FamilyGroup.APPLY,
        MessageConstants.FamilyGroup.APPROVE,
        MessageConstants.FamilyGroup.REJECT,
        MessageConstants.FamilyGroup.JOIN,
        MessageConstants.FamilyGroup.LEAVE,
        MessageConstants.FamilyGroup.REMOVE,
        MessageConstants.FamilyGroup.ROLE_CHANGE,
        MessageConstants.FamilyGroup.DISSOLVE,
        MessageConstants.FamilyGroup.UPDATE
    );

    @Override
    public String getSupportedBusinessType() {
        return MessageConstants.BUSINESS_TYPE_FAMILY_GROUP;
    }

    @Override
    public boolean isSupportedScene(String sceneType) {
        return SUPPORTED_SCENES.contains(sceneType);
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 根据不同场景设置不同的默认优先级
        switch (sceneType) {
            case MessageConstants.FamilyGroup.INVITE:
            case MessageConstants.FamilyGroup.APPLY:
                return MessageConstants.PRIORITY_HIGH; // 邀请和申请优先级较高
            
            case MessageConstants.FamilyGroup.APPROVE:
            case MessageConstants.FamilyGroup.REJECT:
                return MessageConstants.PRIORITY_HIGH; // 审批结果优先级较高
            
            case MessageConstants.FamilyGroup.DISSOLVE:
            case MessageConstants.FamilyGroup.REMOVE:
                return MessageConstants.PRIORITY_URGENT; // 解散和移除优先级最高
            
            case MessageConstants.FamilyGroup.JOIN:
            case MessageConstants.FamilyGroup.LEAVE:
                return MessageConstants.PRIORITY_MEDIUM; // 加入和离开中等优先级
            
            case MessageConstants.FamilyGroup.ROLE_CHANGE:
            case MessageConstants.FamilyGroup.UPDATE:
                return MessageConstants.PRIORITY_LOW; // 角色变更和更新优先级较低
            
            default:
                return MessageConstants.PRIORITY_MEDIUM;
        }
    }

    @Override
    protected Map<String, Object> extractBusinessData(String businessId, String sceneType) {
        Map<String, Object> data = new HashMap<>();

        try {
            // 获取家庭组信息
            FgFamilyGroupPo familyGroup = familyGroupMapper.selectById(businessId);
            if (familyGroup != null) {
                data.put("groupId", familyGroup.getFamilyGroupId());
                data.put("groupName", safeGetString(familyGroup.getFamilyGroupName()));
                data.put("groupDescription", safeGetString(familyGroup.getDescription()));

                // 获取团长信息
                if (familyGroup.getGroupLeaderId() != null) {
                    UserPo leader = userMapper.selectById(familyGroup.getGroupLeaderId());
                    if (leader != null) {
                        data.put("leaderName", safeGetString(leader.getUserName()));
                        data.put("leaderId", leader.getUserId());
                        // 如果是邀请场景，团长就是邀请人
                        data.put("inviterName", safeGetString(leader.getUserName()));
                    }
                }
            }

            // 根据场景类型添加特定数据
            switch (sceneType) {
                case MessageConstants.FamilyGroup.INVITE:
                case MessageConstants.FamilyGroup.APPLY:
                    // 邀请和申请场景，可以从上下文数据中获取邀请人信息
                    break;
                case MessageConstants.FamilyGroup.JOIN:
                case MessageConstants.FamilyGroup.LEAVE:
                    // 成员变动场景，可以从上下文数据中获取成员信息
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("提取家庭组业务数据失败 - businessId: {}, sceneType: {}", businessId, sceneType, e);
        }

        return data;
    }
}
