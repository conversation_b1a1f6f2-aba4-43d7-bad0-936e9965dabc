package com.subfg.api.message.strategy.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.subfg.api.message.strategy.AbstractMessageStrategy;
import com.subfg.common.constans.MessageConstants;
import com.subfg.domain.entity.user.UserPo;
import com.subfg.repository.mapper.UserMapper;

/**
 * 用户消息策略
 */
@Component
public class UserMessageStrategy extends AbstractMessageStrategy {

    @Autowired
    private UserMapper userMapper;

    // 支持的场景类型
    private static final List<String> SUPPORTED_SCENES = Arrays.asList(
        MessageConstants.User.REGISTER,
        MessageConstants.User.LOGIN,
        MessageConstants.User.PASSWORD_RESET,
        MessageConstants.User.ACTIVATE,
        MessageConstants.User.FREEZE,
        MessageConstants.User.UNFREEZE,
        MessageConstants.User.UPDATE,
        MessageConstants.User.VERIFY
    );

    @Override
    public String getSupportedBusinessType() {
        return MessageConstants.BUSINESS_TYPE_USER;
    }

    @Override
    public boolean isSupportedScene(String sceneType) {
        return SUPPORTED_SCENES.contains(sceneType);
    }

    @Override
    public Integer getDefaultPriority(String sceneType) {
        // 根据不同场景设置不同的默认优先级
        switch (sceneType) {
            case MessageConstants.User.REGISTER:
                return MessageConstants.PRIORITY_HIGH; // 注册欢迎优先级较高
            
            case MessageConstants.User.FREEZE:
            case MessageConstants.User.UNFREEZE:
                return MessageConstants.PRIORITY_URGENT; // 账号冻结/解冻优先级最高
            
            case MessageConstants.User.PASSWORD_RESET:
            case MessageConstants.User.ACTIVATE:
                return MessageConstants.PRIORITY_HIGH; // 密码重置和激活优先级较高
            
            case MessageConstants.User.VERIFY:
                return MessageConstants.PRIORITY_MEDIUM; // 实名认证中等优先级
            
            case MessageConstants.User.LOGIN:
            case MessageConstants.User.UPDATE:
                return MessageConstants.PRIORITY_LOW; // 登录和更新优先级较低
            
            default:
                return MessageConstants.PRIORITY_MEDIUM;
        }
    }

    @Override
    protected Map<String, Object> extractBusinessData(String businessId, String sceneType) {
        UserPo userPo = userMapper.selectById(businessId);
        Map<String, Object> data = new HashMap<>();
        
        switch (sceneType) {
             /** 注册场景 */
            case MessageConstants.User.REGISTER:
                data.put("userName", userPo.getUserName());
                break;
        
            default:
                break;
        }
        
        return data;
    }
}
