package com.subfg.api.message.strategy;

import java.util.Map;

/**
 * 消息发送策略接口
 */
public interface MessageStrategy {

    /**
     * 获取支持的业务类型
     */
    String getSupportedBusinessType();

    /**
     * 发送消息（简化版本，只需要业务ID和场景类型，自动提取变量）
     *
     * @param userId 用户ID
     * @param sceneType 场景类型
     * @param businessId 业务ID
     */
    void sendMessage(String userId, String sceneType, String businessId);

    /**
     * 发送消息（支持额外的上下文数据，自动提取变量）
     *
     * @param userId 用户ID
     * @param sceneType 场景类型
     * @param businessId 业务ID
     * @param contextData 上下文数据（补充数据）
     */
    void sendMessage(String userId, String sceneType, String businessId, Map<String, Object> contextData);

    /**
     * 发送消息（支持自定义优先级和上下文数据）
     *
     * @param userId 用户ID
     * @param sceneType 场景类型
     * @param businessId 业务ID
     * @param contextData 上下文数据
     * @param priority 优先级
     */
    void sendMessage(String userId, String sceneType, String businessId, Map<String, Object> contextData, Integer priority);

    /**
     * 验证场景类型是否支持
     *
     * @param sceneType 场景类型
     * @return 是否支持
     */
    boolean isSupportedScene(String sceneType);

    /**
     * 获取默认优先级
     *
     * @param sceneType 场景类型
     * @return 优先级
     */
    default Integer getDefaultPriority(String sceneType) {
        return 2; // 默认中等优先级
    }
}
