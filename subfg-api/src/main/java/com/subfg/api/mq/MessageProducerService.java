package com.subfg.api.mq;

import java.util.UUID;

import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import com.subfg.common.config.RabbitMQConfig;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息生产者服务
 * 提供各种类型的消息发送功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageProducerService {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送用户通知消息
     *
     * @param userId  用户ID
     * @param title   通知标题
     * @param content 通知内容
     * @param type    通知类型
     */
    public void sendUserNotification(String userId, String title, String content, Integer type) {
        try {
            UserNotificationMessage message = UserNotificationMessage.builder()
                    .userId(userId)
                    .title(title)
                    .content(content)
                    .type(type)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DIRECT_EXCHANGE,
                    RabbitMQConfig.USER_NOTIFICATION_ROUTING_KEY,
                    message,
                    correlationData
            );
            
            log.info("用户通知消息发送成功 - userId: {}, title: {}", userId, title);
        } catch (Exception e) {
            log.error("用户通知消息发送失败 - userId: {}, title: {}", userId, title, e);
            throw new RuntimeException("用户通知消息发送失败", e);
        }
    }

    // ==================== 内部消息类定义 ====================

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserNotificationMessage {
        private String userId;
        private String title;
        private String content;
        private Integer type;
        private String businessType;
        private String sceneType;
        private String businessId;
        private Integer priority;
        private java.util.Map<String, Object> extraData;
        private Long timestamp;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class EmailMessage {
        private String to;
        private String subject;
        private String content;
        private Integer type;
        private Long timestamp;
    }


}
