package com.subfg.api.Serivce;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.request.FamilyGroupMemberActivateReq;
import com.subfg.domain.request.FamilyGroupMemberRemoveReq;
import com.subfg.domain.request.GroupPageReq;
import com.subfg.domain.request.LaunchGroupReq;
import com.subfg.domain.request.UpdateFamilyGroupSettingsReq;
import com.subfg.domain.vo.LeaderFamilyGroupVo;
import com.subfg.domain.vo.UserFamilyGroupDetailVo;
import com.subfg.repository.mapper.FgFamilyGroupMapper;
import com.subfg.repository.mapper.FgMemberMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 家庭组团长管理服务接口
 * 提供团长专用的管理功能
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FamilyGroupLeaderService {

    private final FgFamilyGroupMapper fgFamilyGroupMapper;
    private final FgMemberMapper fgMemberMapper;
    /**
     * 获取团长管理的家庭组列表（包含成员激活状态统计）
     *
     * @param req 分页请求参数
     * @return 家庭组列表（包含成员激活状态统计）
     */
    public Page<LeaderFamilyGroupVo> getLeaderGroups(GroupPageReq req){

        String currentUserId = StpUtil.getLoginIdAsString();

        // 计算偏移量
        long offset = (long) (req.getPageNum() - 1) * req.getPageSize();
        long limit = req.getPageSize();

        // 查询团长管理的家庭组列表（包含成员激活状态统计）
        List<LeaderFamilyGroupVo> voList = fgFamilyGroupMapper.selectLeaderGroupsWithMemberStats(
                currentUserId, offset, limit);

        // 查询总数
        Long total = fgFamilyGroupMapper.countLeaderGroups(currentUserId);

        // 填充类型名称和状态名称
        voList.forEach(vo -> {
            vo.setGroupTypeName(getGroupTypeName(vo.getGroupType()));
            vo.setStatusName(getStatusName(vo.getFamilyGroupStatus()));
        });

        // 构建分页结果
        Page<LeaderFamilyGroupVo> pageResult = new Page<>(req.getPageNum(), req.getPageSize());
        pageResult.setRecords(voList);
        pageResult.setTotal(total);
        pageResult.setSize(req.getPageSize());
        pageResult.setCurrent(req.getPageNum());

        return pageResult;
    }

    /**
     * 团长移除指定成员
     * @param req 移除请求
     */
    public void removeMember(FamilyGroupMemberRemoveReq req){
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 验证家庭组存在
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(req.getFamilyGroupId());
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", req.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_NOT_FOUND);
        }

        // 2. 验证当前用户是团长
        if (!currentUserId.equals(familyGroup.getGroupLeaderId())) {
            log.warn("非团长用户尝试移除成员，用户ID：{}，家庭组ID：{}，团长ID：{}",
                currentUserId, req.getFamilyGroupId(), familyGroup.getGroupLeaderId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.PERMISSION_DENIED);
        }

        // 3. 验证被移除的成员存在且状态有效
        FgMemberPo member = fgMemberMapper.selectByFamilyGroupIdAndUserId(req.getFamilyGroupId(), req.getUserId());
        if (member == null) {
            log.warn("成员不存在，家庭组ID：{}，用户ID：{}", req.getFamilyGroupId(), req.getUserId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.NOT_JOINED);
        }

        // 4. 验证成员状态是否允许移除
        if (member.getStatus() == FamilyGroupConstants.MemberStatus.LEFT ||
            member.getStatus() == FamilyGroupConstants.MemberStatus.REMOVED) {
            log.warn("成员已退出或被移除，无法再次移除，家庭组ID：{}，用户ID：{}，当前状态：{}",
                req.getFamilyGroupId(), req.getUserId(), member.getStatus());
            throw new BusinessException("family.group.member.already.removed");
        }

        // 5. 不能移除团长自己
        if (req.getUserId().equals(currentUserId)) {
            log.warn("团长不能移除自己，用户ID：{}，家庭组ID：{}", currentUserId, req.getFamilyGroupId());
            throw new BusinessException("family.group.leader.cannot.remove.self");
        }

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 6. 更新成员状态为被移除，并记录移除原因
        LambdaUpdateWrapper<FgMemberPo> memberUpdateWrapper = new LambdaUpdateWrapper<>();
        memberUpdateWrapper.eq(FgMemberPo::getFamilyGroupId, req.getFamilyGroupId())
                          .eq(FgMemberPo::getUserId, req.getUserId())
                          .set(FgMemberPo::getStatus, FamilyGroupConstants.MemberStatus.REMOVED)
                          .set(FgMemberPo::getRemoveReason, req.getRemoveReason())
                          .set(FgMemberPo::getUpdateTime, currentTime);

        int memberUpdateResult = fgMemberMapper.update(null, memberUpdateWrapper);
        if (memberUpdateResult <= 0) {
            log.error("更新成员状态失败，家庭组ID：{}，用户ID：{}", req.getFamilyGroupId(), req.getUserId());
            throw new BusinessException("family.group.remove.member.failed");
        }

        // 7. 原子性地减少家庭组成员数量
        int updateResult = fgFamilyGroupMapper.decrementMemberCount(req.getFamilyGroupId(), currentTime);
        if (updateResult <= 0) {
            log.warn("家庭组成员数量已为0或家庭组不存在，家庭组ID：{}", req.getFamilyGroupId());
        } else {
            // 8. 检查是否需要关闭家庭组（查询最新的成员数）
            FgFamilyGroupPo updatedFamilyGroup = fgFamilyGroupMapper.selectById(req.getFamilyGroupId());
            if (updatedFamilyGroup != null && updatedFamilyGroup.getCurrentMemberCount() == 0) {
                handleEmptyFamilyGroup(updatedFamilyGroup, currentTime);
            }
        }

    }

    /**
     * 激活成员
     * @param req 激活请求
     */
    public void activateMember(FamilyGroupMemberActivateReq req){
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 验证家庭组存在
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(req.getFamilyGroupId());
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", req.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_NOT_FOUND);
        }

        // 2. 验证当前用户是团长
        if (!currentUserId.equals(familyGroup.getGroupLeaderId())) {
            log.warn("非团长用户尝试激活成员，用户ID：{}，家庭组ID：{}，团长ID：{}",
                currentUserId, req.getFamilyGroupId(), familyGroup.getGroupLeaderId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.PERMISSION_DENIED);
        }

        // 3. 验证被激活的成员存在
        FgMemberPo member = fgMemberMapper.selectByFamilyGroupIdAndUserId(req.getFamilyGroupId(), req.getUserId());
        if (member == null) {
            log.warn("成员不存在，家庭组ID：{}，用户ID：{}", req.getFamilyGroupId(), req.getUserId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.NOT_JOINED);
        }

        // 4. 验证成员状态是否为待激活
        if (member.getStatus() != FamilyGroupConstants.MemberStatus.PENDING) {
            log.warn("成员状态不是待激活，无法激活，家庭组ID：{}，用户ID：{}，当前状态：{}",
                req.getFamilyGroupId(), req.getUserId(), member.getStatus());
            throw new BusinessException("family.group.member.not.pending");
        }

        // 5. 不能激活团长自己（团长应该已经是激活状态）
        if (req.getUserId().equals(currentUserId)) {
            log.warn("团长不能激活自己，用户ID：{}，家庭组ID：{}", currentUserId, req.getFamilyGroupId());
            throw new BusinessException("family.group.leader.cannot.activate.self");
        }

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 6. 更新成员状态为已激活，并记录激活时间
        LambdaUpdateWrapper<FgMemberPo> memberUpdateWrapper = new LambdaUpdateWrapper<>();
        memberUpdateWrapper.eq(FgMemberPo::getFamilyGroupId, req.getFamilyGroupId())
                          .eq(FgMemberPo::getUserId, req.getUserId())
                          .set(FgMemberPo::getStatus, FamilyGroupConstants.MemberStatus.ACTIVATED)
                          .set(FgMemberPo::getActiveTime, currentTime)
                          .set(FgMemberPo::getUpdateTime, currentTime);

        int memberUpdateResult = fgMemberMapper.update(null, memberUpdateWrapper);
        if (memberUpdateResult <= 0) {
            log.error("更新成员状态失败，家庭组ID：{}，用户ID：{}", req.getFamilyGroupId(), req.getUserId());
            throw new BusinessException("family.group.activate.member.failed");
        }

    }

    /**
     * 处理空家庭组（成员数为0的情况）
     */
    private void handleEmptyFamilyGroup(FgFamilyGroupPo familyGroup, Long currentTime) {

        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, familyGroup.getFamilyGroupId())
                    .set(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.CLOSED)
                    .set(FgFamilyGroupPo::getUpdateTime, currentTime);

        int updateResult = fgFamilyGroupMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            log.error("关闭空家庭组失败，家庭组ID：{}", familyGroup.getFamilyGroupId());
            throw new BusinessException("family.group.close.failed");
        }
    }

    /**
     * 获取家庭组详情信息(团长视角)
     * @param familyGroupId 家庭组ID
     * @return 家庭组详情信息
     */
    public UserFamilyGroupDetailVo getFamilyGroupDetail(String familyGroupId) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 参数校验
        if (!StringUtils.hasText(familyGroupId)) {
            log.warn("家庭组ID不能为空");
            throw new BusinessException("family.group.id.required");
        }

        // 2. 查询家庭组详情
        UserFamilyGroupDetailVo detailVo = fgFamilyGroupMapper.selectFamilyGroupDetailById(familyGroupId);

        if (detailVo == null || detailVo.getFamilyGroup() == null) {
            log.warn("家庭组不存在，家庭组ID：{}", familyGroupId);
            throw new BusinessException("family.group.not.found");
        }

        // 3. 验证当前用户是团长
        if (!currentUserId.equals(detailVo.getFamilyGroup().getGroupLeaderId())) {
            log.warn("非团长用户尝试获取家庭组详情，用户ID：{}，家庭组ID：{}，团长ID：{}",
                currentUserId, familyGroupId, detailVo.getFamilyGroup().getGroupLeaderId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.PERMISSION_DENIED);
        }

        return detailVo;
    }

    /**
     * 修改家庭组设置(完善审核信息)
     * @param req 更新家庭组设置请求
     */
    public void updateGroupSettings(UpdateFamilyGroupSettingsReq req) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 验证家庭组存在
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(req.getFamilyGroupId());
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", req.getFamilyGroupId());
            throw new BusinessException("family.group.not.found");
        }

        // 2. 验证当前用户是团长
        if (!currentUserId.equals(familyGroup.getGroupLeaderId())) {
            log.warn("非团长用户尝试修改家庭组设置，用户ID：{}，家庭组ID：{}，团长ID：{}",
                currentUserId, req.getFamilyGroupId(), familyGroup.getGroupLeaderId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.PERMISSION_DENIED);
        }

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 3. 构建更新条件，只更新允许修改的字段
        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, req.getFamilyGroupId())
                    .set(FgFamilyGroupPo::getUpdateTime, currentTime);

        boolean hasUpdate = false;

        // 4. 更新家庭组名称（如果提供）
        if (StringUtils.hasText(req.getFamilyGroupName())) {
            updateWrapper.set(FgFamilyGroupPo::getFamilyGroupName, req.getFamilyGroupName().trim());
            hasUpdate = true;
        }

        // 5. 更新家庭组描述（如果提供）
        if (req.getDescription() != null) {
            updateWrapper.set(FgFamilyGroupPo::getDescription,
                StringUtils.hasText(req.getDescription()) ? req.getDescription().trim() : null);
            hasUpdate = true;
        }

        // 6. 更新审核图片（如果提供）
        if (req.getReviewPicture() != null) {
            updateWrapper.set(FgFamilyGroupPo::getReviewPicture,
                StringUtils.hasText(req.getReviewPicture()) ? req.getReviewPicture().trim() : null);
            hasUpdate = true;
        }

        // 7. 更新标签（如果提供）
        if (req.getTags() != null) {
            updateWrapper.set(FgFamilyGroupPo::getTags, req.getTags());
            hasUpdate = true;
        }

        // 8. 执行更新（只有在有字段需要更新时才执行）
        if (hasUpdate) {
            int updateResult = fgFamilyGroupMapper.update(null, updateWrapper);
            if (updateResult <= 0) {
                log.error("更新家庭组设置失败，家庭组ID：{}", req.getFamilyGroupId());
                throw new BusinessException("family.group.update.settings.failed");
            }
            log.info("团长成功修改家庭组设置，家庭组ID：{}，团长ID：{}，备注：{}",
                req.getFamilyGroupId(), currentUserId, req.getRemark());
        } else {
            log.info("家庭组设置无变更，家庭组ID：{}，团长ID：{}", req.getFamilyGroupId(), currentUserId);
        }
    }

    /**
     * 发车操作
     */
    public void launchGroup(LaunchGroupReq req) {
        String currentUserId = StpUtil.getLoginIdAsString();
        String familyGroupId = req.getFamilyGroupId();
        // 验证家庭组存在
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(familyGroupId);
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", familyGroupId);
            throw new BusinessException("family.group.not.found");
        }
        
        // 验证家庭组类型为拼团
        if (familyGroup.getGroupType() != FamilyGroupConstants.GroupType.GROUP_BUYING) {
            log.warn("家庭组类型不是拼团，家庭组ID：{}", familyGroupId);
            throw new BusinessException("family.group.not.group.buying");
        }

        // 验证家庭组状态为组建状态
        if (familyGroup.getFamilyGroupStatus() != FamilyGroupConstants.Status.BUILDING) {
            log.warn("家庭组状态不是已审核，家庭组ID：{}", familyGroupId);
            throw new BusinessException("family.group.not.review.passed");
        }

        // 验证当前用户是团长
        if (!currentUserId.equals(familyGroup.getGroupLeaderId())) {
            log.warn("非团长用户尝试发车，用户ID：{}，家庭组ID：{}，团长ID：{}",
                currentUserId, familyGroupId, familyGroup.getGroupLeaderId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.PERMISSION_DENIED);
        }

        // 更新家庭组状态为待审核
        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, familyGroupId)
                    .set(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.REVIEWING)
                    .set(FgFamilyGroupPo::getReviewPicture, req.getReviewPicture())
                    .set(FgFamilyGroupPo::getReviewRemark, req.getReviewRemark())
                    .set(FgFamilyGroupPo::getUpdateTime, TimeUtil.getCurrentTimestamp());

        int updateResult = fgFamilyGroupMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            log.error("更新家庭组状态失败，家庭组ID：{}", familyGroupId);
            throw new BusinessException("family.group.launch.failed");
        }
    }

    /**
     * 关闭家庭组
     * @param groupType
     * @return
     */
    public void closeGroup(String familyGroupId) {
        String currentUserId = StpUtil.getLoginIdAsString();
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(familyGroupId);
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", familyGroupId);
            throw new BusinessException("family.group.not.found");
        }
    }

    /**
     * 获取家庭组类型名称
     */
    private String getGroupTypeName(Integer groupType) {
        if (groupType == null) {
            return null;
        }
        switch (groupType) {
            case FamilyGroupConstants.GroupType.SELF_BUILT:
                return "自建家庭组";
            case FamilyGroupConstants.GroupType.GROUP_BUYING:
                return "拼团家庭组";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取家庭组状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case FamilyGroupConstants.Status.REVIEWING:
                return "审核中";
            case FamilyGroupConstants.Status.BUILDING:
                return "组建中";
            case FamilyGroupConstants.Status.LAUNCHED:
                return "已发车";
            case FamilyGroupConstants.Status.CLOSED:
                return "已关闭";
            case FamilyGroupConstants.Status.REVIEW_REJECTED:
                return "审核未通过";
            default:
                return "未知状态";
        }
    }

}
