package com.subfg.api.Serivce;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.subfg.common.constans.OssConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.OssUtil;
import com.subfg.domain.entity.user.UserPo;
import com.subfg.repository.mapper.UserMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FileService {

    private final UserMapper userMapper;
    private final OssUtil ossUtil;

    /**
     * 用户上传头像
     */
    public void uploadAvatar(MultipartFile file) {
        String currentUserId = StpUtil.getLoginIdAsString();
        try {
            String fileUrl = ossUtil.uploadFile(file, OssConstants.UploadDirectory.USER_AVATAR);
            userMapper.update(null, new LambdaUpdateWrapper<UserPo>()
            .eq(UserPo::getUserId, currentUserId)
            .set(UserPo::getAvatarUrl, fileUrl));
        } catch (Exception e) {
            log.error("上传头像失败: {}", e.getMessage(), e);
            throw new BusinessException("上传头像失败");
        }
    }

}
