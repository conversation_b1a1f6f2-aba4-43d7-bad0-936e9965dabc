package com.subfg.api.Serivce;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.common.constans.MessageConstants;
import com.subfg.domain.entity.message.UserMessagePo;
import com.subfg.domain.request.MessagePageReq;
import com.subfg.domain.vo.UnreadCountVo;
import com.subfg.domain.vo.UserMessageVo;
import com.subfg.repository.mapper.UserMessageMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageService {

    private final UserMessageMapper userMessageMapper;

    /**
     * 获取当前用户未读消息数量
     *
     * @return 未读消息数量统计
     */
    public UnreadCountVo getUnreadCount() {
        // 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 查询总未读数量
        Long totalUnreadCount = userMessageMapper.countUnreadMessages(currentUserId);

        // 查询系统通知未读数量
        Long systemUnreadCount = userMessageMapper.countUnreadMessagesByType(
            currentUserId, MessageConstants.MESSAGE_TYPE_SYSTEM);

        // 查询业务通知未读数量
        Long businessUnreadCount = userMessageMapper.countUnreadMessagesByType(
            currentUserId, MessageConstants.MESSAGE_TYPE_BUSINESS);

        return new UnreadCountVo(totalUnreadCount, systemUnreadCount, businessUnreadCount);
    }

    /**
     * 分页查询当前用户消息列表
     *
     * @param req 分页查询请求参数
     * @return 分页消息列表
     */
    public IPage<UserMessageVo> getMessagePage(MessagePageReq req) {
        // 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 构建分页对象
        Page<UserMessagePo> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 查询用户消息列表
        IPage<UserMessagePo> messagePoPage = userMessageMapper.selectUserMessages(
            page, currentUserId, req.getReadStatus(), req.getMessageType(),
            req.getBusinessType(), req.getSceneType(), req.getPriority(),
            req.getOrderBy());

        // 转换为VO对象
        List<UserMessageVo> messageVoList = messagePoPage.getRecords().stream()
            .map(this::convertToVo)
            .collect(Collectors.toList());

        // 构建返回结果
        Page<UserMessageVo> resultPage = new Page<>(req.getPageNum(), req.getPageSize());
        resultPage.setRecords(messageVoList);
        resultPage.setTotal(messagePoPage.getTotal());

        return resultPage;
    }

    /**
     * 将UserMessagePo转换为UserMessageVo
     *
     * @param messagePo 消息实体
     * @return 消息VO
     */
    private UserMessageVo convertToVo(UserMessagePo messagePo) {
        UserMessageVo messageVo = new UserMessageVo();
        messageVo.setMessageId(messagePo.getMessageId());
        messageVo.setTitle(messagePo.getTitle());
        messageVo.setContent(messagePo.getContent());
        messageVo.setMessageType(messagePo.getMessageType());
        messageVo.setMessageTypeDesc(getMessageTypeDesc(messagePo.getMessageType()));
        messageVo.setReadStatus(messagePo.getReadStatus());
        messageVo.setReadStatusDesc(getReadStatusDesc(messagePo.getReadStatus()));
        messageVo.setReadTime(messagePo.getReadTime());
        messageVo.setBusinessType(messagePo.getBusinessType());
        messageVo.setSceneType(messagePo.getSceneType());
        messageVo.setBusinessId(messagePo.getBusinessId());
        messageVo.setExtraData(messagePo.getExtraData());
        messageVo.setPriority(messagePo.getPriority());
        messageVo.setPriorityDesc(getPriorityDesc(messagePo.getPriority()));
        messageVo.setSendTime(messagePo.getSendTime());
        messageVo.setCreateTime(messagePo.getCreateTime());
        return messageVo;
    }

    /**
     * 获取消息类型描述
     */
    private String getMessageTypeDesc(Integer messageType) {
        if (messageType == null) {
            return "未知";
        }
        switch (messageType) {
            case MessageConstants.MESSAGE_TYPE_SYSTEM:
                return "系统通知";
            case MessageConstants.MESSAGE_TYPE_BUSINESS:
                return "业务通知";
            default:
                return "未知";
        }
    }

    /**
     * 获取读取状态描述
     */
    private String getReadStatusDesc(Integer readStatus) {
        if (readStatus == null) {
            return "未知";
        }
        switch (readStatus) {
            case MessageConstants.READ_STATUS_UNREAD:
                return "未读";
            case MessageConstants.READ_STATUS_READ:
                return "已读";
            default:
                return "未知";
        }
    }

    /**
     * 获取优先级描述
     */
    private String getPriorityDesc(Integer priority) {
        if (priority == null) {
            return "未知";
        }
        switch (priority) {
            case MessageConstants.PRIORITY_LOW:
                return "低";
            case MessageConstants.PRIORITY_MEDIUM:
                return "中";
            case MessageConstants.PRIORITY_HIGH:
                return "高";
            case MessageConstants.PRIORITY_URGENT:
                return "紧急";
            default:
                return "未知";
        }
    }
}
