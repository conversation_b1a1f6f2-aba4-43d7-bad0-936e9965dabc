package com.subfg.api.Serivce;

import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.common.util.OssUtil;
import com.subfg.domain.entity.product.ProductCategoryPo;
import com.subfg.domain.entity.product.ProductPo;
import com.subfg.domain.entity.product.ProductRegionPo;
import com.subfg.domain.request.ProductCategoryPageReq;
import com.subfg.domain.request.ProductPageReq;
import com.subfg.domain.request.ProductRegionPageReq;
import com.subfg.domain.vo.BillingCycleVo;
import com.subfg.repository.mapper.ProductCategoryMapper;
import com.subfg.repository.mapper.ProductMapper;
import com.subfg.repository.mapper.ProductRegionMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductService {

    private final ProductMapper productMapper;
    private final ProductCategoryMapper productCategoryMapper;
    private final ProductRegionMapper productRegionMapper;
    private final OssUtil ossUtil;

    /**
     * 分页获取产品列表
     */
    public Page<ProductPo> page(ProductPageReq req) {

        // 构建查询条件
        LambdaQueryWrapper<ProductPo> queryWrapper = new LambdaQueryWrapper<>();

        // 产品名称关键字模糊搜索
        if (StringUtils.hasText(req.getKeyword())) {
            queryWrapper.like(ProductPo::getName, req.getKeyword().trim());
        }

        // 公司名称
        if (StringUtils.hasText(req.getCompany())) {
            queryWrapper.eq(ProductPo::getCompany, req.getCompany().trim());
        }

        // 产品分类
        if (req.getCategory() != null) {
            queryWrapper.eq(ProductPo::getCategory, req.getCategory());
        }

        // 按ID倒序排列
        queryWrapper.orderByDesc(ProductPo::getId);
        queryWrapper.eq(ProductPo::getEnable, true);

        // 分页查询
        Page<ProductPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<ProductPo> result = productMapper.selectPage(page, queryWrapper);

        // 处理产品图标URL，将相对路径转换为完整URL
        result.getRecords().forEach(product -> {
            if (StringUtils.hasText(product.getIconUrl())) {
                try {
                    String fullIconUrl = ossUtil.buildStaticFileUrl(product.getIconUrl());
                    product.setIconUrl(fullIconUrl);
                } catch (Exception e) {
                    log.warn("处理产品图标URL失败，产品ID: {}, 原始URL: {}, 错误: {}",
                            product.getId(), product.getIconUrl(), e.getMessage());
                }
            }
        });

        return result;
    }

    /**
     * 分页获取产品分类列表
     */
    public Page<ProductCategoryPo> categoryPage(ProductCategoryPageReq req) {

        // 构建查询条件
        LambdaQueryWrapper<ProductCategoryPo> queryWrapper = new LambdaQueryWrapper<>();

        // 分类名称关键字模糊搜索
        if (StringUtils.hasText(req.getKeyword())) {
            queryWrapper.like(ProductCategoryPo::getName, req.getKeyword().trim())
                       .or()
                       .like(ProductCategoryPo::getNameCn, req.getKeyword().trim());
        }

        // 是否启用
        if (req.getEnable() != null) {
            queryWrapper.eq(ProductCategoryPo::getEnable, req.getEnable());
        }

        // 是否热门
        if (req.getPopular() != null) {
            queryWrapper.eq(ProductCategoryPo::getPopular, req.getPopular());
        }


        // 按ID倒序排列
        queryWrapper.orderByDesc(ProductCategoryPo::getId);

        // 分页查询
        Page<ProductCategoryPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<ProductCategoryPo> result = productCategoryMapper.selectPage(page, queryWrapper);

        // 处理产品分类图标URL，将相对路径转换为完整URL
        result.getRecords().forEach(category -> {
            if (StringUtils.hasText(category.getIconUrl())) {
                try {
                    String fullIconUrl = ossUtil.buildStaticFileUrl(category.getIconUrl());
                    category.setIconUrl(fullIconUrl);
                } catch (Exception e) {
                    log.warn("处理产品分类图标URL失败，分类ID: {}, 原始URL: {}, 错误: {}",
                            category.getId(), category.getIconUrl(), e.getMessage());
                }
            }
        });

        return result;
    }

    /**
     * 分页获取国家/地区列表
     */
    public Page<ProductRegionPo> regionPage(ProductRegionPageReq req) {

        // 构建查询条件
        LambdaQueryWrapper<ProductRegionPo> queryWrapper = new LambdaQueryWrapper<>();

        // 地区名称关键字模糊搜索
        if (StringUtils.hasText(req.getKeyword())) {
            queryWrapper.like(ProductRegionPo::getName, req.getKeyword().trim())
                       .or()
                       .like(ProductRegionPo::getNameCn, req.getKeyword().trim());
        }

        // 国家代码
        if (StringUtils.hasText(req.getCountryCode())) {
            queryWrapper.eq(ProductRegionPo::getCountryCode, req.getCountryCode().trim());
        }

        // 地区代码
        if (StringUtils.hasText(req.getAreaCode())) {
            queryWrapper.eq(ProductRegionPo::getAreaCode, req.getAreaCode().trim());
        }

        // 是否启用
        if (req.getEnable() != null) {
            queryWrapper.eq(ProductRegionPo::getEnable, req.getEnable());
        }

        // 是否热门
        if (req.getPopular() != null) {
            queryWrapper.eq(ProductRegionPo::getPopular, req.getPopular());
        }


        // 按ID倒序排列
        queryWrapper.orderByDesc(ProductRegionPo::getId);

        // 分页查询
        Page<ProductRegionPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<ProductRegionPo> result = productRegionMapper.selectPage(page, queryWrapper);

        // 处理产品地区图标URL，将相对路径转换为完整URL
        result.getRecords().forEach(region -> {
            if (StringUtils.hasText(region.getIconUrl())) {
                try {
                    String fullIconUrl = ossUtil.buildStaticFileUrl(region.getIconUrl());
                    region.setIconUrl(fullIconUrl);
                } catch (Exception e) {
                    log.warn("处理产品地区图标URL失败，地区ID: {}, 原始URL: {}, 错误: {}",
                            region.getId(), region.getIconUrl(), e.getMessage());
                }
            }
        });

        return result;
    }

    /**
     * 获取计费周期列表
     */
    public List<BillingCycleVo> billingCycleList(){
        return Arrays.asList(
                new BillingCycleVo().setName("1个月").setValue("1").setDescription("1个月"),
                new BillingCycleVo().setName("3个月").setValue("2").setDescription("3个月（季度）"),
                new BillingCycleVo().setName("6个月").setValue("3").setDescription("6个月（半年）"),
                new BillingCycleVo().setName("12个月").setValue("4").setDescription("12个月（1年）"),
                new BillingCycleVo().setName("24个月").setValue("5").setDescription("24个月（2年）"),
                new BillingCycleVo().setName("36个月").setValue("6").setDescription("36个月（3年）")
        );
    }
        

}
