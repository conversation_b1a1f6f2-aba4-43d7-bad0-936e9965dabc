package com.subfg.api.Serivce;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.request.GroupPageReq;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.domain.vo.MyJoinedFamilyGroupVo;
import com.subfg.domain.vo.UserFamilyGroupDetailVo;
import com.subfg.repository.mapper.FgFamilyGroupMapper;
import com.subfg.repository.mapper.FgMemberMapper;
import com.subfg.repository.mapper.UserMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户家庭组服务类
 * 处理用户个人相关的家庭组操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFamilyGroupService {

    private final FgFamilyGroupMapper fgFamilyGroupMapper;
    private final FgMemberMapper fgMemberMapper;
    private final UserMapper userMapper;

    /**
     * 获取当前用户创建的家庭组分页列表
     */
    public Page<FamilyGroupVo> getMyCreatedFamilyGroups(GroupPageReq req) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 构建查询条件
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getCreateUserId, currentUserId)
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        // 分页查询
        Page<FgFamilyGroupPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<FgFamilyGroupPo> result = fgFamilyGroupMapper.selectPage(page, queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = result.getRecords().stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        // 构建分页结果
        Page<FamilyGroupVo> pageResult = new Page<>(req.getPageNum(), req.getPageSize());
        pageResult.setRecords(voList);
        pageResult.setTotal(result.getTotal());
        pageResult.setSize(result.getSize());
        pageResult.setCurrent(result.getCurrent());

        log.info("用户创建的家庭组分页列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return pageResult;
    }

    /**
     * 获取当前用户加入的家庭组分页列表（不包括团长是自己的）
     */
    public Page<MyJoinedFamilyGroupVo> getMyJoinedFamilyGroups(GroupPageReq req) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 先查询用户的成员记录
        List<FgMemberPo> memberList = fgMemberMapper.selectByUserId(currentUserId);

        // 获取家庭组ID列表
        List<String> familyGroupIds = memberList.stream()
                .map(FgMemberPo::getFamilyGroupId)
                .collect(Collectors.toList());

        if (familyGroupIds.isEmpty()) {
            // 返回空的分页结果
            Page<MyJoinedFamilyGroupVo> emptyPage = new Page<>(req.getPageNum(), req.getPageSize());
            emptyPage.setRecords(List.of());
            emptyPage.setTotal(0);
            return emptyPage;
        }

        // 构建查询条件，排除团长是自己的
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FgFamilyGroupPo::getFamilyGroupId, familyGroupIds)
                   .ne(FgFamilyGroupPo::getGroupLeaderId, currentUserId) // 排除团长是自己的
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        // 分页查询
        Page<FgFamilyGroupPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<FgFamilyGroupPo> result = fgFamilyGroupMapper.selectPage(page, queryWrapper);

        // 创建成员记录的映射，方便查找用户在各个家庭组中的状态
        Map<String, FgMemberPo> memberMap = memberList.stream()
                .collect(Collectors.toMap(FgMemberPo::getFamilyGroupId, member -> member));

        // 转换为VO对象
        List<MyJoinedFamilyGroupVo> voList = result.getRecords().stream()
                .map(po -> convertToMyJoinedFamilyGroupVo(po, memberMap.get(po.getFamilyGroupId())))
                .collect(Collectors.toList());

        // 构建分页结果
        Page<MyJoinedFamilyGroupVo> pageResult = new Page<>(req.getPageNum(), req.getPageSize());
        pageResult.setRecords(voList);
        pageResult.setTotal(result.getTotal());
        pageResult.setSize(result.getSize());
        pageResult.setCurrent(result.getCurrent());

        log.info("用户加入的家庭组分页列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return pageResult;
    }

    /**
     * 获取当前用户加入的拼团家庭组分页列表
     */
    public Page<FamilyGroupVo> getMyJoinedGroupBuyingFamilyGroups(GroupPageReq req) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 先查询用户的成员记录
        List<FgMemberPo> memberList = fgMemberMapper.selectByUserId(currentUserId);

        // 获取家庭组ID列表
        List<String> familyGroupIds = memberList.stream()
                .map(FgMemberPo::getFamilyGroupId)
                .collect(Collectors.toList());

        if (familyGroupIds.isEmpty()) {
            // 返回空的分页结果
            Page<FamilyGroupVo> emptyPage = new Page<>(req.getPageNum(), req.getPageSize());
            emptyPage.setRecords(List.of());
            emptyPage.setTotal(0);
            return emptyPage;
        }

        // 构建查询条件，查询拼团家庭组信息，状态为组建中
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FgFamilyGroupPo::getFamilyGroupId, familyGroupIds)
                   .eq(FgFamilyGroupPo::getGroupType, FamilyGroupConstants.GroupType.GROUP_BUYING) // 只查询拼团家庭组
                   .eq(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.BUILDING) // 状态为组建中
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        // 分页查询
        Page<FgFamilyGroupPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<FgFamilyGroupPo> result = fgFamilyGroupMapper.selectPage(page, queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = result.getRecords().stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        // 构建分页结果
        Page<FamilyGroupVo> pageResult = new Page<>(req.getPageNum(), req.getPageSize());
        pageResult.setRecords(voList);
        pageResult.setTotal(result.getTotal());
        pageResult.setSize(result.getSize());
        pageResult.setCurrent(result.getCurrent());

        log.info("用户加入的拼团家庭组分页列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return pageResult;
    }

    /**
     * 用户加入家庭组
     * 根据家庭组类型分别处理拼团和自建家庭组的不同业务逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void joinFamilyGroup(String familyGroupId) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 查询家庭组信息
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(familyGroupId);
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", familyGroupId);
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_NOT_FOUND);
        }

        // 2. 验证基本条件
        validateJoinConditions(familyGroup, currentUserId);

        // 3. 根据家庭组类型分别处理
        if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.GROUP_BUYING) {
            // 拼团家庭组加入逻辑
            joinGroupBuyingFamilyGroup(familyGroup, currentUserId);
        } else if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.SELF_BUILT) {
            // 自建家庭组加入逻辑
            joinSelfBuiltFamilyGroup(familyGroup, currentUserId);
        } else {
            log.error("未知的家庭组类型，家庭组ID：{}，类型：{}", familyGroupId, familyGroup.getGroupType());
            throw new BusinessException("family.group.invalid.type");
        }
    }

    /**
     * 验证用户加入家庭组的基本条件
     */
    private void validateJoinConditions(FgFamilyGroupPo familyGroup, String currentUserId) {
        // 1. 检查家庭组状态是否允许加入
        if (familyGroup.getFamilyGroupStatus() != FamilyGroupConstants.Status.BUILDING) {
            log.warn("家庭组状态不允许加入，家庭组ID：{}，状态：{}",
                familyGroup.getFamilyGroupId(), familyGroup.getFamilyGroupStatus());
            throw new BusinessException("family.group.status.not.allow.join");
        }

        // 2. 检查是否为团长或创建者（不能加入自己的家庭组）
        if (currentUserId.equals(familyGroup.getGroupLeaderId()) ||
            currentUserId.equals(familyGroup.getCreateUserId())) {
            log.warn("不能加入自己创建或担任团长的家庭组，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException("family.group.cannot.join.own");
        }

        // 3. 检查是否已经加入该家庭组
        FgMemberPo existingMember = fgMemberMapper.selectByFamilyGroupIdAndUserId(
            familyGroup.getFamilyGroupId(), currentUserId);
        if (existingMember != null) {
            log.warn("用户已加入该家庭组，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.ALREADY_JOINED);
        }

        // 4. 检查家庭组是否已满
        if (familyGroup.getCurrentMemberCount() >= familyGroup.getSumVacancy()) {
            log.warn("家庭组已满，家庭组ID：{}，当前成员数：{}，总空位数：{}",
                familyGroup.getFamilyGroupId(),
                familyGroup.getCurrentMemberCount(),
                familyGroup.getSumVacancy());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_FULL);
        }

        // 5. 对于拼团家庭组，检查是否已过截止时间
        if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.GROUP_BUYING) {
            if (familyGroup.getDeadline() != null &&
                familyGroup.getDeadline() <= TimeUtil.getCurrentTimestamp()) {
                log.warn("拼团家庭组已过截止时间，家庭组ID：{}，截止时间：{}",
                    familyGroup.getFamilyGroupId(),
                    TimeUtil.timestampToDateTime(familyGroup.getDeadline()));
                throw new BusinessException("family.group.deadline.expired");
            }
        }
    }

    /**
     * 拼团家庭组加入逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    private void joinGroupBuyingFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId) {
        log.info("用户加入拼团家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 1. 创建成员记录（拼团家庭组成员直接激活）
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroup.getFamilyGroupId())
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.ACTIVATED) // 拼团家庭组成员直接激活
            .setInvitationTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int insertResult = fgMemberMapper.insert(member);
        if (insertResult <= 0) {
            log.error("创建拼团家庭组成员记录失败，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        // 2. 使用原子操作更新家庭组成员数量
        int updateResult = fgFamilyGroupMapper.incrementMemberCount(familyGroup.getFamilyGroupId(), currentTime);
        if (updateResult <= 0) {
            log.error("更新拼团家庭组成员数量失败，家庭组ID：{}",
                familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        // 3. 更新用户加入家庭组数量
        userMapper.incrementJoinFgCount(currentUserId);

        // 如果已有团长 发送付款通知给当前用户
        if(familyGroup.getGroupLeaderId() != null){
            // TODO: 发送付款通知给当前用户
            // sendPaymentNoticeToUser(familyGroup, currentUserId);
        }
        
        // 如果加入后车队满员 发送通知给所有成员
        if(familyGroup.getCurrentMemberCount() == familyGroup.getSumVacancy()){
            // TODO: 发送满员通知给所有成员
            // sendFullNoticeToAllMembers(familyGroup);
        }

        log.info("用户成功加入拼团家庭组，用户ID：{}，家庭组ID：{}",
            currentUserId, familyGroup.getFamilyGroupId());
    }

    /**
     * 自建家庭组加入逻辑
     */
    private void joinSelfBuiltFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId) {
        log.info("用户加入自建家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 1. 创建成员记录（自建家庭组成员待激活）
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroup.getFamilyGroupId())
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.PENDING) // 自建家庭组成员待激活
            .setInvitationTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int insertResult = fgMemberMapper.insert(member);
        if (insertResult <= 0) {
            log.error("创建自建家庭组成员记录失败，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        // 2. 使用原子操作更新家庭组成员数量和最新加入时间
        int updateResult = fgFamilyGroupMapper.incrementMemberCountWithJoinTime(
            familyGroup.getFamilyGroupId(), currentTime, currentTime);
        if (updateResult <= 0) {
            log.error("更新自建家庭组成员数量失败，家庭组ID：{}",
                familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        // 3. 更新用户加入家庭组数量
        userMapper.incrementJoinFgCount(currentUserId);

    }

    /**
     * 用户退出家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public void exitFamilyGroup(String familyGroupId) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 查询家庭组信息
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(familyGroupId);
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", familyGroupId);
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_NOT_FOUND);
        }

        // 2. 验证退出条件
        validateExitConditions(familyGroup, currentUserId);

        // 3. 根据家庭组类型分别处理退出逻辑
        if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.GROUP_BUYING) {
            // 拼团家庭组退出逻辑
            exitGroupBuyingFamilyGroup(familyGroup, currentUserId);
        } else if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.SELF_BUILT) {
            // 自建家庭组退出逻辑
            exitSelfBuiltFamilyGroup(familyGroup, currentUserId);
        } else {
            log.error("未知的家庭组类型，家庭组ID：{}，类型：{}", familyGroupId, familyGroup.getGroupType());
            throw new BusinessException("family.group.invalid.type");
        }

    }

    /**
     * 验证用户退出家庭组的条件
     */
    private void validateExitConditions(FgFamilyGroupPo familyGroup, String currentUserId) {
        // 1. 检查是否为创建者（创建者不能退出自己的家庭组）
        if (currentUserId.equals(familyGroup.getCreateUserId())) {
            log.warn("创建者不能退出自己的家庭组，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException("family.group.creator.cannot.exit");
        }

        // 2. 检查是否为团长（团长不能退出自己的家庭组）
        if (currentUserId.equals(familyGroup.getGroupLeaderId())) {
            log.warn("团长不能退出自己的家庭组，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException("family.group.leader.cannot.exit");
        }

        // 3. 检查是否已加入该家庭组
        FgMemberPo existingMember = fgMemberMapper.selectByFamilyGroupIdAndUserId(
            familyGroup.getFamilyGroupId(), currentUserId);
        if (existingMember == null) {
            log.warn("用户未加入该家庭组，无法退出，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.NOT_JOINED);
        }

        // 4. 检查成员状态是否允许退出
        if (existingMember.getStatus() == FamilyGroupConstants.MemberStatus.LEFT ||
            existingMember.getStatus() == FamilyGroupConstants.MemberStatus.REMOVED) {
            log.warn("用户已退出或被移除，用户ID：{}，家庭组ID：{}，成员状态：{}",
                currentUserId, familyGroup.getFamilyGroupId(), existingMember.getStatus());
            throw new BusinessException("family.group.already.left");
        }

        // 5. 检查家庭组状态是否允许退出
        if (familyGroup.getFamilyGroupStatus() == FamilyGroupConstants.Status.CLOSED) {
            log.warn("家庭组已关闭，无法退出，家庭组ID：{}，状态：{}",
                familyGroup.getFamilyGroupId(), familyGroup.getFamilyGroupStatus());
            throw new BusinessException("family.group.closed.cannot.exit");
        }
    }

    /**
     * 拼团家庭组退出逻辑
     */
    private void exitGroupBuyingFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId) {
        log.info("用户退出拼团家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());
        
        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 根据拼团家庭组的不同状态处理不同的退出逻辑
        if (familyGroup.getFamilyGroupStatus() == FamilyGroupConstants.Status.LAUNCHED) {
            // 已发车的拼团家庭组：需要计算退款金额并处理退款逻辑
            exitLaunchedGroupBuyingFamilyGroup(familyGroup, currentUserId, currentTime);
        } else {
            // 未发车的拼团家庭组：直接退出，无需退款
            exitUnlaunchedGroupBuyingFamilyGroup(familyGroup, currentUserId, currentTime);
        }
    }

    /**
     * 退出已发车的拼团家庭组（需要计算退款）
     */
    private void exitLaunchedGroupBuyingFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId, Long currentTime) {
        log.info("用户退出已发车的拼团家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        // 1. 计算退款金额（按使用天数比例计算）
        BigDecimal refundAmount = calculateRefundAmount(familyGroup, currentTime);
        log.info("计算退款金额，用户ID：{}，家庭组ID：{}，退款金额：{}",
            currentUserId, familyGroup.getFamilyGroupId(), refundAmount);

        // 2. 更新成员状态为已退出
        updateMemberStatus(familyGroup.getFamilyGroupId(), currentUserId,
            FamilyGroupConstants.MemberStatus.LEFT, currentTime);

        // 3. 使用原子操作更新家庭组成员数量
        int updateResult = fgFamilyGroupMapper.decrementMemberCount(familyGroup.getFamilyGroupId(), currentTime);
        if (updateResult <= 0) {
            log.warn("家庭组成员数量已为0或家庭组不存在，家庭组ID：{}", familyGroup.getFamilyGroupId());
            // 这种情况下仍然继续处理退款，但不需要检查空家庭组
        } else {
            // 4. 检查是否需要关闭家庭组（查询最新的成员数）
            FgFamilyGroupPo updatedFamilyGroup = fgFamilyGroupMapper.selectById(familyGroup.getFamilyGroupId());
            if (updatedFamilyGroup != null && updatedFamilyGroup.getCurrentMemberCount() == 0) {
                handleEmptyFamilyGroup(updatedFamilyGroup, currentTime);
            }
        }

        // 5. 触发退款流程（异步处理）
        triggerRefundProcess(familyGroup, currentUserId, refundAmount, currentTime);

        log.info("用户成功退出已发车的拼团家庭组，用户ID：{}，家庭组ID：{}，退款金额：{}",
            currentUserId, familyGroup.getFamilyGroupId(), refundAmount);
    }

    /**
     * 退出未发车的拼团家庭组（无需退款）
     */
    private void exitUnlaunchedGroupBuyingFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId, Long currentTime) {
        log.info("用户退出未发车的拼团家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        // 1. 更新成员状态为已退出
        updateMemberStatus(familyGroup.getFamilyGroupId(), currentUserId,
            FamilyGroupConstants.MemberStatus.LEFT, currentTime);

        // 2. 使用原子操作更新家庭组成员数量
        int updateResult = fgFamilyGroupMapper.decrementMemberCount(familyGroup.getFamilyGroupId(), currentTime);
        if (updateResult <= 0) {
            log.warn("家庭组成员数量已为0或家庭组不存在，家庭组ID：{}", familyGroup.getFamilyGroupId());
        } else {
            // 3. 检查是否需要关闭家庭组（查询最新的成员数）
            FgFamilyGroupPo updatedFamilyGroup = fgFamilyGroupMapper.selectById(familyGroup.getFamilyGroupId());
            if (updatedFamilyGroup != null && updatedFamilyGroup.getCurrentMemberCount() == 0) {
                handleEmptyFamilyGroup(updatedFamilyGroup, currentTime);
            }
        }

        log.info("用户成功退出未发车的拼团家庭组，用户ID：{}，家庭组ID：{}",
            currentUserId, familyGroup.getFamilyGroupId());
    }

    /**
     * 自建家庭组退出逻辑
     */
    private void exitSelfBuiltFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId) {
        
        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 根据自建家庭组的不同状态处理不同的退出逻辑
        if (familyGroup.getFamilyGroupStatus() == FamilyGroupConstants.Status.LAUNCHED) {
            // 已发车的自建家庭组：需要计算退款金额
            exitLaunchedSelfBuiltFamilyGroup(familyGroup, currentUserId, currentTime);
        } else {
            // 未发车的自建家庭组：直接退出，无需退款
            exitUnlaunchedSelfBuiltFamilyGroup(familyGroup, currentUserId, currentTime);
        }
    }

    /**
     * 退出已发车的自建家庭组（需要计算退款）
     */
    private void exitLaunchedSelfBuiltFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId, Long currentTime) {
        log.info("用户退出已发车的自建家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        // 1. 计算退款金额（按使用天数比例计算）
        BigDecimal refundAmount = calculateRefundAmount(familyGroup, currentTime);
        log.info("计算退款金额，用户ID：{}，家庭组ID：{}，退款金额：{}",
            currentUserId, familyGroup.getFamilyGroupId(), refundAmount);

        // 2. 更新成员状态为已退出
        updateMemberStatus(familyGroup.getFamilyGroupId(), currentUserId,
            FamilyGroupConstants.MemberStatus.LEFT, currentTime);

        // 3. 使用原子操作更新家庭组成员数量
        int updateResult = fgFamilyGroupMapper.decrementMemberCount(familyGroup.getFamilyGroupId(), currentTime);
        if (updateResult <= 0) {
            log.warn("家庭组成员数量已为0或家庭组不存在，家庭组ID：{}", familyGroup.getFamilyGroupId());
            // 这种情况下仍然继续处理退款，但不需要检查空家庭组
        } else {
            // 4. 检查是否需要关闭家庭组（查询最新的成员数）
            FgFamilyGroupPo updatedFamilyGroup = fgFamilyGroupMapper.selectById(familyGroup.getFamilyGroupId());
            if (updatedFamilyGroup != null && updatedFamilyGroup.getCurrentMemberCount() == 0) {
                handleEmptyFamilyGroup(updatedFamilyGroup, currentTime);
            }
        }

        // 5. 触发退款流程（异步处理）
        triggerRefundProcess(familyGroup, currentUserId, refundAmount, currentTime);

        log.info("用户成功退出已发车的自建家庭组，用户ID：{}，家庭组ID：{}，退款金额：{}",
            currentUserId, familyGroup.getFamilyGroupId(), refundAmount);
    }

    /**
     * 退出未发车的自建家庭组（无需退款）
     */
    private void exitUnlaunchedSelfBuiltFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId, Long currentTime) {
        log.info("用户退出未发车的自建家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        // 1. 更新成员状态为已退出
        updateMemberStatus(familyGroup.getFamilyGroupId(), currentUserId,
            FamilyGroupConstants.MemberStatus.LEFT, currentTime);

        // 2. 使用原子操作更新家庭组成员数量
        int updateResult = fgFamilyGroupMapper.decrementMemberCount(familyGroup.getFamilyGroupId(), currentTime);
        if (updateResult <= 0) {
            log.warn("家庭组成员数量已为0或家庭组不存在，家庭组ID：{}", familyGroup.getFamilyGroupId());
        } else {
            // 3. 检查是否需要关闭家庭组（查询最新的成员数）
            FgFamilyGroupPo updatedFamilyGroup = fgFamilyGroupMapper.selectById(familyGroup.getFamilyGroupId());
            if (updatedFamilyGroup != null && updatedFamilyGroup.getCurrentMemberCount() == 0) {
                handleEmptyFamilyGroup(updatedFamilyGroup, currentTime);
            }
        }

    }

    /**
     * 计算退款金额
     */
    private BigDecimal calculateRefundAmount(FgFamilyGroupPo familyGroup, Long currentTime) {
        // 基本参数检查
        if (familyGroup.getAmount() == null || familyGroup.getLaunchTime() == null) {
            log.warn("无法计算退款金额，缺少必要参数，家庭组ID：{}", familyGroup.getFamilyGroupId());
            return BigDecimal.ZERO;
        }

        // 计算已使用天数
        long usedDays = (currentTime - familyGroup.getLaunchTime()) / (24 * 60 * 60);
        
        // 计算总天数（根据计费周期）
        int totalDays = getTotalDaysByBillingCycle(familyGroup.getBillingCycle());
        
        // 计算退款比例
        if (usedDays >= totalDays) {
            // 已使用天数超过总天数，不退款
            return BigDecimal.ZERO;
        }
        
        // 计算退款金额（按剩余天数比例）
        BigDecimal refundRatio = BigDecimal.valueOf(totalDays - usedDays)
            .divide(BigDecimal.valueOf(totalDays), 4, RoundingMode.HALF_UP);
        
        BigDecimal refundAmount = BigDecimal.valueOf(familyGroup.getAmount().longValue())
            .multiply(refundRatio)
            .setScale(2, RoundingMode.HALF_UP);
        
        return refundAmount;
    }

    /**
     * 根据计费周期获取总天数
     */
    private int getTotalDaysByBillingCycle(Integer billingCycle) {
        if (billingCycle == null) {
            return 30; // 默认30天
        }
        
        switch (billingCycle) {
            case FamilyGroupConstants.BillingCycle.MONTHLY:
                return 30;
            case FamilyGroupConstants.BillingCycle.QUARTERLY:
                return 90;
            case FamilyGroupConstants.BillingCycle.SEMI_ANNUAL:
                return 180;
            case FamilyGroupConstants.BillingCycle.ANNUAL:
                return 365;
            case FamilyGroupConstants.BillingCycle.BIENNIAL:
                return 730;
            case FamilyGroupConstants.BillingCycle.TRIENNIAL:
                return 1095;
            default:
                return 30;
        }
    }

    /**
     * 触发退款流程（异步处理）
     */
    private void triggerRefundProcess(FgFamilyGroupPo familyGroup, String currentUserId, 
                                     BigDecimal refundAmount, Long currentTime) {
        if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {

            
            // TODO: 实现退款逻辑，包括：
            // 1. 创建退款记录
            // 2. 调用支付系统退款接口
            // 3. 记录退款日志
            // 4. 发送退款通知
            
            // 这里暂时只记录日志，实际退款逻辑需要根据支付系统的API来实现
            log.info("退款流程已触发，待异步处理，用户ID：{}，家庭组ID：{}，退款金额：{}", 
                currentUserId, familyGroup.getFamilyGroupId(), refundAmount);
        } else {
            log.info("退款金额为0，无需触发退款流程，用户ID：{}，家庭组ID：{}", 
                currentUserId, familyGroup.getFamilyGroupId());
        }
    }

    /**
     * 更新成员状态
     */
    private void updateMemberStatus(String familyGroupId, String userId, Integer status, Long currentTime) {
        LambdaUpdateWrapper<FgMemberPo> memberUpdateWrapper = new LambdaUpdateWrapper<>();
        memberUpdateWrapper.eq(FgMemberPo::getFamilyGroupId, familyGroupId)
                          .eq(FgMemberPo::getUserId, userId)
                          .set(FgMemberPo::getStatus, status)
                          .set(FgMemberPo::getUpdateTime, currentTime);

        int memberUpdateResult = fgMemberMapper.update(null, memberUpdateWrapper);
        if (memberUpdateResult <= 0) {
            log.error("更新成员状态失败，用户ID：{}，家庭组ID：{}", userId, familyGroupId);
            throw new BusinessException(FamilyGroupConstants.ErrorCode.LEAVE_FAILED);
        }
    }



    /**
     * 处理空家庭组（成员数为0的情况）
     */
    private void handleEmptyFamilyGroup(FgFamilyGroupPo familyGroup, Long currentTime) {

        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, familyGroup.getFamilyGroupId())
                    .set(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.CLOSED)
                    .set(FgFamilyGroupPo::getUpdateTime, currentTime);

        int updateResult = fgFamilyGroupMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            log.error("关闭空家庭组失败，家庭组ID：{}", familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.LEAVE_FAILED);
        }

    }

    /**
     * 成为团长
     */
    @Transactional(rollbackFor = Exception.class)
    public void becomeLeader(String familyGroupId) {
        String currentUserId = StpUtil.getLoginIdAsString();

        // 1. 验证家庭组存在
        FgFamilyGroupPo familyGroup = fgFamilyGroupMapper.selectById(familyGroupId);
        if (familyGroup == null) {
            throw new BusinessException("family.group.not.found");
        }

        // 验证家庭组为拼团家庭组
        if (familyGroup.getGroupType() != FamilyGroupConstants.GroupType.GROUP_BUYING) {
            throw new BusinessException("family.group.not.group.buying");
        }
        
        // TODO: 加锁更新家庭组信息 通知车友付款

    }

    /**
     * 将实体对象转换为VO对象
     */
    private FamilyGroupVo convertToVo(FgFamilyGroupPo po) {
        FamilyGroupVo vo = new FamilyGroupVo();
        vo.setFamilyGroupId(po.getFamilyGroupId());
        vo.setFamilyGroupName(po.getFamilyGroupName());
        vo.setDescription(po.getDescription());
        vo.setGroupType(po.getGroupType());
        vo.setGroupTypeName(getGroupTypeName(po.getGroupType()));
        vo.setFamilyGroupStatus(po.getFamilyGroupStatus());
        vo.setStatusName(getStatusName(po.getFamilyGroupStatus()));
        vo.setProductId(po.getProductId());
        vo.setRegionId(po.getRegionId());
        vo.setPlanId(po.getPlanId());
        vo.setAmount(po.getAmount());
        vo.setBillingCycle(po.getBillingCycle());
        vo.setCurrentMemberCount(po.getCurrentMemberCount());
        vo.setSumVacancy(po.getSumVacancy());
        vo.setDeadline(po.getDeadline());
        vo.setGroupLeaderId(po.getGroupLeaderId());
        vo.setCreateUserId(po.getCreateUserId());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setLatestJoinTime(po.getLatestJoinTime());
        vo.setLaunchTime(po.getLaunchTime());
        vo.setIsConverted(po.getIsConverted());
        return vo;
    }

    /**
     * 将实体对象转换为我加入的家庭组VO对象
     */
    private MyJoinedFamilyGroupVo convertToMyJoinedFamilyGroupVo(FgFamilyGroupPo po, FgMemberPo memberPo) {
        MyJoinedFamilyGroupVo vo = new MyJoinedFamilyGroupVo();

        // 设置家庭组基本信息
        vo.setFamilyGroupId(po.getFamilyGroupId());
        vo.setFamilyGroupName(po.getFamilyGroupName());
        vo.setDescription(po.getDescription());
        vo.setGroupType(po.getGroupType());
        vo.setGroupTypeName(getGroupTypeName(po.getGroupType()));
        vo.setFamilyGroupStatus(po.getFamilyGroupStatus());
        vo.setStatusName(getStatusName(po.getFamilyGroupStatus()));
        vo.setProductId(po.getProductId());
        vo.setRegionId(po.getRegionId());
        vo.setPlanId(po.getPlanId());
        vo.setAmount(po.getAmount());
        vo.setBillingCycle(po.getBillingCycle());
        vo.setCurrentMemberCount(po.getCurrentMemberCount());
        vo.setSumVacancy(po.getSumVacancy());
        vo.setDeadline(po.getDeadline());
        vo.setGroupLeaderId(po.getGroupLeaderId());
        vo.setCreateUserId(po.getCreateUserId());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setLatestJoinTime(po.getLatestJoinTime());
        vo.setLaunchTime(po.getLaunchTime());
        vo.setIsConverted(po.getIsConverted());

        // 设置成员状态相关信息
        if (memberPo != null) {
            vo.setMemberStatus(memberPo.getStatus());
            vo.setMemberStatusName(getMemberStatusName(memberPo.getStatus()));
            vo.setIsActivated(memberPo.getStatus() == FamilyGroupConstants.MemberStatus.ACTIVATED);
            vo.setJoinTime(memberPo.getCreateTime());
            vo.setActiveTime(memberPo.getActiveTime());
        } else {
            // 如果成员记录不存在，设置默认值
            vo.setMemberStatus(null);
            vo.setMemberStatusName("未知状态");
            vo.setIsActivated(false);
            vo.setJoinTime(null);
            vo.setActiveTime(null);
        }

        return vo;
    }

    /**
     * 获取成员状态名称
     */
    private String getMemberStatusName(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        switch (status) {
            case FamilyGroupConstants.MemberStatus.PENDING:
                return "待激活";
            case FamilyGroupConstants.MemberStatus.ACTIVATED:
                return "已激活";
            case FamilyGroupConstants.MemberStatus.LEFT:
                return "已退出";
            case FamilyGroupConstants.MemberStatus.REMOVED:
                return "被移除";
            case FamilyGroupConstants.MemberStatus.EXPIRED:
                return "已过期";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取家庭组类型名称
     */
    private String getGroupTypeName(Integer groupType) {
        if (groupType == null) {
            return null;
        }
        switch (groupType) {
            case FamilyGroupConstants.GroupType.SELF_BUILT:
                return "自建家庭组";
            case FamilyGroupConstants.GroupType.GROUP_BUYING:
                return "拼团家庭组";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取家庭组状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case FamilyGroupConstants.Status.REVIEWING:
                return "审核中";
            case FamilyGroupConstants.Status.BUILDING:
                return "组建中";
            case FamilyGroupConstants.Status.LAUNCHED:
                return "已发车";
            case FamilyGroupConstants.Status.CLOSED:
                return "已关闭";
            case FamilyGroupConstants.Status.REVIEW_REJECTED:
                return "审核未通过";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取家庭组详情信息
     */
    public UserFamilyGroupDetailVo getFamilyGroupDetail(String familyGroupId) {

        // 参数校验
        if (!StringUtils.hasText(familyGroupId)) {
            throw new BusinessException("family.group.id.required");
        }

        // 查询家庭组详情
        UserFamilyGroupDetailVo detailVo = fgFamilyGroupMapper.selectFamilyGroupDetailById(familyGroupId);

        if (detailVo == null || detailVo.getFamilyGroup() == null) {
            throw new BusinessException("family.group.not.found");
        }

        return detailVo;
    }
}
