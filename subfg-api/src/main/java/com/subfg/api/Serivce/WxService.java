package com.subfg.api.Serivce;


import org.springframework.stereotype.Service;

import com.subfg.common.constans.ThirdPartyConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.domain.dto.wx.WxAccessTokenDto;
import com.subfg.domain.dto.wx.WxUserDetailDto;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WxService {

    /**
     * 根据回调code获取accesstoken
     */
    public WxAccessTokenDto getWxAccessToken(String code){
        String url = ThirdPartyConstants.ApiUrl.WECHAT_TOKEN_URL + "?appid=" + ThirdPartyConstants.ConfigKey.WECHAT_APP_ID + "&secret=" + ThirdPartyConstants.ConfigKey.WECHAT_APP_SECRET + "&code=" + code + "&grant_type=authorization_code";
        HttpResponse response = HttpRequest.get(url).execute();
        if(response.getStatus() != 200 || response.body().contains("{\"errcode\":")){
            log.error("获取微信access_token失败,code:{}", code);
            throw new BusinessException("wx.get.access.token.failed");
        }
        return JSONUtil.toBean(response.body(), WxAccessTokenDto.class);
    }

    /**
     * 根据accesstoken获取用户信息
     */
    public WxUserDetailDto getWxUserInfo(String accessToken, String openId){
        String url = ThirdPartyConstants.ApiUrl.WECHAT_USER_INFO_URL + "?access_token=" + accessToken + "&openid=" + openId;
        HttpResponse response = HttpRequest.get(url).execute();
        if(response.getStatus() != 200 || response.body().contains("{\"errcode\":")){
            log.error("获取微信用户信息失败,accessToken:{}", accessToken);
            throw new BusinessException("wx.get.user.info.failed");
        }
        return JSONUtil.toBean(response.body(), WxUserDetailDto.class);
    }

}
