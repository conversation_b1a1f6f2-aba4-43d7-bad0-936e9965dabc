-- 用户表建表SQL
-- 对应实体类：com.subfg.domain.entity.user.UserPo

CREATE TABLE `user` (
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID（主键）',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `phone` BIGINT DEFAULT NULL COMMENT '手机号',
    `wechat_unionid` VARCHAR(100) DEFAULT NULL COMMENT '微信Unionid',
    `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（MD5加密）',
    `salt` VARCHAR(100) DEFAULT NULL COMMENT '密码盐值',
    `role` VARCHAR(50) DEFAULT NULL COMMENT '用户角色',
    `user_name` VARCHAR(100) DEFAULT NULL COMMENT '用户名',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `motto` VARCHAR(500) DEFAULT NULL COMMENT '个人描述',
    `credit_score` DECIMAL(10,2) DEFAULT NULL COMMENT '信用分数',
    `tags` J<PERSON><PERSON> DEFAULT NULL COMMENT '用户标签',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `create_ip` VARCHAR(50) DEFAULT NULL COMMENT '创建时的IP地址',
    `last_online_time` BIGINT DEFAULT NULL COMMENT '最后在线时间',
    `delete_time` BIGINT DEFAULT NULL COMMENT '删除时间',
    `enable` TINYINT(1) DEFAULT 1 COMMENT '是否启用（1:启用 0:禁用）',
    `custom_subM_count` INT DEFAULT 0 COMMENT '自定义订阅数量',
    `create_fg_count` INT DEFAULT 0 COMMENT '创建家庭组数量',
    `join_fg_count` INT DEFAULT 0 COMMENT '加入家庭组数量',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    UNIQUE KEY `uk_wechat_unionid` (`wechat_unionid`),
    KEY `idx_user_name` (`user_name`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_enable` (`enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
