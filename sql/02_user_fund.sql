-- 用户资金表建表SQL
-- 对应实体类：com.subfg.domain.entity.user.UserFundPo

CREATE TABLE `user_fund` (
    `fund_id` VARCHAR(64) NOT NULL COMMENT '钱包ID（主键）',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `total_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总金额',
    `withdraw_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '可提现金额',
    `currency_type` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    `total_income` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总收入',
    `total_expense` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总支出',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    `version` INT DEFAULT 0 COMMENT '乐观锁版本号',
    PRIMARY KEY (`fund_id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_total_amount` (`total_amount`),
    KEY `idx_currency_type` (`currency_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金表';
