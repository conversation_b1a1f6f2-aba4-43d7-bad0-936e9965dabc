-- 用户资金流水表建表SQL
-- 对应实体类：com.subfg.domain.entity.user.UserFundTransactionPo

CREATE TABLE `user_fund_transaction` (
    `transaction_id` VARCHAR(64) NOT NULL COMMENT '流水ID（主键）',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `transaction_type` INT NOT NULL COMMENT '交易类型（1:收入 2:支出 3:提现）',
    `amount` DECIMAL(15,2) NOT NULL COMMENT '交易金额',
    `balance_before` DECIMAL(15,2) NOT NULL COMMENT '交易前余额',
    `balance_after` DECIMAL(15,2) NOT NULL COMMENT '交易后余额',
    `status` INT DEFAULT 1 COMMENT '交易状态（0:处理中 1:成功 2:失败）',
    `order_no` VARCHAR(64) DEFAULT NULL COMMENT '关联订单号',
    `currency_type` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '交易备注',
    `third_transaction_no` VARCHAR(128) DEFAULT NULL COMMENT '第三方交易号',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    `complete_time` BIGINT DEFAULT NULL COMMENT '完成时间',
    `version` INT DEFAULT 0 COMMENT '乐观锁版本号',
    PRIMARY KEY (`transaction_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_transaction_type` (`transaction_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_third_transaction_no` (`third_transaction_no`),
    KEY `idx_user_type_time` (`user_id`, `transaction_type`, `create_time`),
    KEY `idx_user_status_time` (`user_id`, `status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金流水表';

-- 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE `user_fund_transaction` ADD CONSTRAINT `fk_user_fund_transaction_user_id` 
-- FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 创建分区表（可选，用于大数据量场景）
-- 按月分区，提高查询性能
-- ALTER TABLE `user_fund_transaction` 
-- PARTITION BY RANGE (YEAR(FROM_UNIXTIME(create_time/1000)) * 100 + MONTH(FROM_UNIXTIME(create_time/1000))) (
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p202502 VALUES LESS THAN (202503),
--     PARTITION p202503 VALUES LESS THAN (202504),
--     PARTITION p202504 VALUES LESS THAN (202505),
--     PARTITION p202505 VALUES LESS THAN (202506),
--     PARTITION p202506 VALUES LESS THAN (202507),
--     PARTITION p202507 VALUES LESS THAN (202508),
--     PARTITION p202508 VALUES LESS THAN (202509),
--     PARTITION p202509 VALUES LESS THAN (202510),
--     PARTITION p202510 VALUES LESS THAN (202511),
--     PARTITION p202511 VALUES LESS THAN (202512),
--     PARTITION p202512 VALUES LESS THAN (202601),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 插入示例数据（可选）
-- INSERT INTO `user_fund_transaction` (
--     `transaction_id`, `user_id`, `transaction_type`, `amount`, 
--     `balance_before`, `balance_after`, `status`, `currency_type`, 
--     `remark`, `create_time`, `update_time`, `complete_time`
-- ) VALUES 
-- ('TXN001', 'USER001', 1, 100.00, 0.00, 100.00, 1, 'CNY', '充值', 1706745600000, 1706745600000, 1706745600000),
-- ('TXN002', 'USER001', 2, 50.00, 100.00, 50.00, 1, 'CNY', '消费', 1706745660000, 1706745660000, 1706745660000),
-- ('TXN003', 'USER001', 3, 30.00, 50.00, 20.00, 1, 'CNY', '提现', 1706745720000, 1706745720000, 1706745720000);
