-- 订单表建表SQL
-- 对应实体类：com.subfg.domain.entity.order.OrderPo

CREATE TABLE `order` (
    `order_id` VARCHAR(64) NOT NULL COMMENT '订单ID（主键）',
    `order_no` VARCHAR(32) NOT NULL COMMENT '订单号（业务编号，对外展示）',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `order_type` INT NOT NULL COMMENT '订单类型（1:自建家庭组订单 2:拼团家庭组订单 3:续费订单）',
    `order_status` INT NOT NULL DEFAULT 1 COMMENT '订单状态（1:待支付 2:支付中 3:已支付 4:处理中 5:已完成 6:已取消 7:已退款 8:退款中 9:已过期 10:支付失败）',
    
    -- 产品信息
    `product_id` INT DEFAULT NULL COMMENT '产品ID',
    `product_name` VARCHAR(200) DEFAULT NULL COMMENT '产品名称（冗余字段）',
    `plan_id` INT DEFAULT NULL COMMENT '套餐ID',
    `plan_name` VARCHAR(200) DEFAULT NULL COMMENT '套餐名称（冗余字段）',
    `region_id` INT DEFAULT NULL COMMENT '地区ID',
    `billing_cycle` INT DEFAULT NULL COMMENT '计费周期（月）',
    
    -- 家庭组信息
    `family_group_id` VARCHAR(64) DEFAULT NULL COMMENT '家庭组ID',
    `member_id` VARCHAR(64) DEFAULT NULL COMMENT '成员ID',
    
    -- 金额信息
    `original_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '订单原价',
    `discount_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '优惠金额',
    `total_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
    `paid_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '实际支付金额',
    `currency_type` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    
    -- 支付信息
    `payment_method` INT DEFAULT NULL COMMENT '支付方式（1:钱包支付 2:微信支付 3:支付宝 4:银行卡 5:PayPal 6:信用卡 7:Apple Pay 8:Google Pay 9:混合支付）',
    `third_party_order_no` VARCHAR(128) DEFAULT NULL COMMENT '第三方支付单号',
    `third_party_transaction_no` VARCHAR(128) DEFAULT NULL COMMENT '第三方交易号',
    
    -- 优惠信息
    `coupon_id` VARCHAR(64) DEFAULT NULL COMMENT '优惠券ID',
    `coupon_code` VARCHAR(50) DEFAULT NULL COMMENT '优惠券代码',
    `promotion_id` VARCHAR(64) DEFAULT NULL COMMENT '促销活动ID',
    
    -- 订单描述
    `order_title` VARCHAR(500) DEFAULT NULL COMMENT '订单标题',
    `order_description` TEXT DEFAULT NULL COMMENT '订单描述',
    `remark` VARCHAR(1000) DEFAULT NULL COMMENT '备注信息',
    
    -- 时间信息
    `expire_time` BIGINT DEFAULT NULL COMMENT '订单过期时间',
    `paid_time` BIGINT DEFAULT NULL COMMENT '支付时间',
    `completed_time` BIGINT DEFAULT NULL COMMENT '完成时间',
    `cancelled_time` BIGINT DEFAULT NULL COMMENT '取消时间',
    `refunded_time` BIGINT DEFAULT NULL COMMENT '退款时间',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    `version` INT DEFAULT 0 COMMENT '乐观锁版本号',
    
    PRIMARY KEY (`order_id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_order_status` (`order_status`),
    KEY `idx_order_type` (`order_type`),
    KEY `idx_family_group_id` (`family_group_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_paid_time` (`paid_time`),
    KEY `idx_expire_time` (`expire_time`),
    KEY `idx_third_party_order_no` (`third_party_order_no`),
    KEY `idx_user_status_time` (`user_id`, `order_status`, `create_time`),
    KEY `idx_user_type_time` (`user_id`, `order_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE `order` ADD CONSTRAINT `fk_order_user_id` 
-- FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- ALTER TABLE `order` ADD CONSTRAINT `fk_order_family_group_id` 
-- FOREIGN KEY (`family_group_id`) REFERENCES `fg_family_group` (`family_group_id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 创建分区表（可选，用于大数据量场景）
-- 按月分区，提高查询性能
-- ALTER TABLE `order` 
-- PARTITION BY RANGE (YEAR(FROM_UNIXTIME(create_time/1000)) * 100 + MONTH(FROM_UNIXTIME(create_time/1000))) (
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p202502 VALUES LESS THAN (202503),
--     PARTITION p202503 VALUES LESS THAN (202504),
--     PARTITION p202504 VALUES LESS THAN (202505),
--     PARTITION p202505 VALUES LESS THAN (202506),
--     PARTITION p202506 VALUES LESS THAN (202507),
--     PARTITION p202507 VALUES LESS THAN (202508),
--     PARTITION p202508 VALUES LESS THAN (202509),
--     PARTITION p202509 VALUES LESS THAN (202510),
--     PARTITION p202510 VALUES LESS THAN (202511),
--     PARTITION p202511 VALUES LESS THAN (202512),
--     PARTITION p202512 VALUES LESS THAN (202601),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 插入示例数据（可选）
-- INSERT INTO `order` (
--     `order_id`, `order_no`, `user_id`, `order_type`, `order_status`,
--     `product_id`, `product_name`, `original_amount`, `total_amount`,
--     `currency_type`, `order_title`, `create_time`, `update_time`, `version`
-- ) VALUES 
-- ('ORD001', 'O202501310001', 'USER001', 1, 1, 1, 'Netflix Premium', 99.00, 99.00, 'CNY', 'Netflix Premium 自建家庭组订阅', 1706745600000, 1706745600000, 0),
-- ('ORD002', 'O202501310002', 'USER002', 2, 3, 2, 'Spotify Family', 59.00, 59.00, 'CNY', 'Spotify Family 拼团家庭组订阅', 1706745660000, 1706745660000, 0),
-- ('ORD003', 'O202501310003', 'USER003', 3, 5, 1, 'Netflix Premium', 99.00, 99.00, 'CNY', 'Netflix Premium 续费订单', 1706745720000, 1706745720000, 0);
