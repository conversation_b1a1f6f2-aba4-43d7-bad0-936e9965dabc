-- 用户消息记录表建表SQL
-- 对应实体类：com.subfg.domain.entity.message.UserMessagePo

CREATE TABLE `user_message` (
    -- 基础字段
    `message_id` VARCHAR(64) NOT NULL COMMENT '消息ID（主键）',
    `user_id` VARCHAR(64) NOT NULL COMMENT '接收用户ID',
    `title` VARCHAR(200) NOT NULL COMMENT '消息标题',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `message_type` TINYINT NOT NULL COMMENT '消息类型（1-系统通知 2-业务通知）',
    
    -- 状态字段
    `read_status` TINYINT DEFAULT 0 COMMENT '已读状态（0-未读 1-已读）',
    `read_time` BIGINT DEFAULT NULL COMMENT '阅读时间',
    
    -- 业务关联字段
    `business_type` VARCHAR(50) DEFAULT NULL COMMENT '业务类型（如：FAMILY_GROUP、ORDER、PAYMENT等）',
    `scene_type` VARCHAR(50) DEFAULT NULL COMMENT '场景类型（如：INVITE、APPROVE、REJECT等）',
    `business_id` VARCHAR(64) DEFAULT NULL COMMENT '业务ID（关联的业务数据ID）',
    
    -- 扩展字段
    `extra_data` JSON DEFAULT NULL COMMENT '扩展数据（JSON格式）',
    `priority` TINYINT DEFAULT 1 COMMENT '优先级（1-低 2-中 3-高 4-紧急）',
    
    -- 时间字段
    `send_time` BIGINT NOT NULL COMMENT '发送时间',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    
    -- 主键和索引
    PRIMARY KEY (`message_id`),
    
    -- 核心查询索引：用户未读消息查询
    KEY `idx_user_unread` (`user_id`, `read_status`, `send_time` DESC),
    
    -- 用户消息类型查询
    KEY `idx_user_type` (`user_id`, `message_type`, `send_time` DESC),
    
    -- 业务关联查询
    KEY `idx_business` (`business_type`, `business_id`, `send_time` DESC),

    -- 业务场景查询
    KEY `idx_business_scene` (`business_type`, `scene_type`, `send_time` DESC),
    
    -- 时间查询
    KEY `idx_send_time` (`send_time`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户消息记录表';
