-- 家庭组表建表SQL
-- 对应实体类：com.subfg.domain.entity.fg.FgFamilyGroupPo

CREATE TABLE `fg_family_group` (
    `family_group_id` VARCHAR(64) NOT NULL COMMENT '家庭组ID（主键）',
    `family_group_name` VARCHAR(100) DEFAULT NULL COMMENT '家庭组名称',
    `description` TEXT DEFAULT NULL COMMENT '家庭组描述',
    `group_type` INT DEFAULT NULL COMMENT '家庭组类型（1-自建家庭组，2-拼团家庭组）',
    `family_group_status` INT DEFAULT NULL COMMENT '家庭组状态（0-审核中，1-组建中，2-发车中，3-关闭（团长关闭），-1-审核未通过）',
    `invite_code` VARCHAR(50) DEFAULT NULL COMMENT '邀请码',
    `tags` JSON DEFAULT NULL COMMENT '家庭组标签',
    `product_id` INT DEFAULT NULL COMMENT '产品ID',
    `category_id` VARCHAR(64) DEFAULT NULL COMMENT '产品分类ID',
    `region_id` INT DEFAULT NULL COMMENT '地区ID',
    `plan_id` INT DEFAULT NULL COMMENT '套餐ID',
    `amount` DECIMAL(10,2) DEFAULT NULL COMMENT '订阅价格',
    `billing_cycle` INT DEFAULT NULL COMMENT '计费周期（月）',
    `current_member_count` INT DEFAULT NULL COMMENT '当前成员数',
    `sum_vacancy` INT DEFAULT NULL COMMENT '总空位数',
    `deadline` BIGINT DEFAULT NULL COMMENT '截止发车时间（拼团家庭组专用）',
    `group_leader_id` VARCHAR(64) DEFAULT NULL COMMENT '团长用户ID',
    `create_user_id` VARCHAR(64) DEFAULT NULL COMMENT '创建用户ID',
    `create_time` BIGINT DEFAULT NULL COMMENT '创建时间',
    `update_time` BIGINT DEFAULT NULL COMMENT '更新时间',
    `latest_join_time` BIGINT DEFAULT NULL COMMENT '最新加入时间（自建家庭组专用）',
    `launch_time` BIGINT DEFAULT NULL COMMENT '发车时间 - 拼团转为自建时候设置（拼团家庭组专用）',
    `subscribe_start_time` BIGINT DEFAULT NULL COMMENT '订阅开始时间',
    `subscribe_end_time` BIGINT DEFAULT NULL COMMENT '订阅结束时间',
    `review_time` BIGINT DEFAULT NULL COMMENT '审核时间',
    `review_user` VARCHAR(64) DEFAULT NULL COMMENT '审核用户',
    `review_remark` VARCHAR(500) DEFAULT NULL COMMENT '审核备注',
    `review_picture` VARCHAR(500) DEFAULT NULL COMMENT '审核图片（自建家庭组专用 - 后拼团转自建也需要使用）',
    `is_converted` INT DEFAULT NULL COMMENT '是否已转换（0-未转换，1-已转换，拼团家庭组专用）',
    PRIMARY KEY (`family_group_id`),
    KEY `idx_group_type` (`group_type`),
    KEY `idx_family_group_status` (`family_group_status`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_group_leader_id` (`group_leader_id`),
    KEY `idx_create_user_id` (`create_user_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_invite_code` (`invite_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭组表';