-- 消息模板表建表SQL
-- 对应实体类：com.subfg.domain.entity.message.MessageTemplatePo

CREATE TABLE `message_template` (
    -- 基础字段
    `template_id` VARCHAR(64) NOT NULL COMMENT '模板ID（主键）',
    `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `business_type` VARCHAR(50) NOT NULL COMMENT '业务类型（FAMILY_GROUP、ORDER、PAYMENT等）',
    `scene_type` VARCHAR(50) NOT NULL COMMENT '场景类型（INVITE、APPROVE、REJECT、CREATE等）',
    `message_type` TINYINT NOT NULL COMMENT '消息类型（1-系统通知 2-业务通知）',
    
    -- 模板内容
    `title_template` VARCHAR(200) NOT NULL COMMENT '标题模板（支持变量占位符）',
    `content_template` TEXT NOT NULL COMMENT '内容模板（支持变量占位符）',

    -- 变量定义
    `required_variables` JSON DEFAULT NULL COMMENT '所需变量定义（JSON格式，定义模板需要哪些变量）',

    -- 配置字段
    `enable` TINYINT(1) DEFAULT 1 COMMENT '是否启用（1-启用 0-禁用）',
    
    -- 时间字段
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    
    -- 主键和索引
    PRIMARY KEY (`template_id`),
    
    -- 业务场景查询索引
    UNIQUE KEY `uk_business_scene` (`business_type`, `scene_type`),
    
    -- 业务类型查询
    KEY `idx_business_type` (`business_type`, `enable`),
    
    -- 消息类型查询
    KEY `idx_message_type` (`message_type`, `enable`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息模板表';

-- 插入初始化模板数据
INSERT INTO message_template (template_id, template_name, business_type, scene_type, message_type,
                            title_template, content_template, required_variables, enable, create_time, update_time) VALUES

-- 家庭组邀请
('tpl_fg_invite_001', '家庭组邀请通知', 'FAMILY_GROUP', 'INVITE', 2,
 '家庭组邀请', '${inviterName}邀请您加入家庭组"${groupName}"，快来一起享受家庭时光吧！',
 '["inviterName", "groupName"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 家庭组审批通过
('tpl_fg_approve_001', '家庭组申请通过', 'FAMILY_GROUP', 'APPROVE', 2,
 '申请通过', '恭喜！您加入家庭组"${groupName}"的申请已通过，现在可以开始使用家庭组功能了。',
 '["groupName"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 家庭组审批拒绝
('tpl_fg_reject_001', '家庭组申请被拒绝', 'FAMILY_GROUP', 'REJECT', 2,
 '申请被拒绝', '很抱歉，您加入家庭组"${groupName}"的申请被拒绝。拒绝原因：${reason}',
 '["groupName", "reason"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 家庭组成员加入
('tpl_fg_join_001', '新成员加入通知', 'FAMILY_GROUP', 'JOIN', 2,
 '新成员加入', '${memberName}已加入家庭组"${groupName}"，欢迎新成员！',
 '["memberName", "groupName"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 订单创建
('tpl_order_create_001', '订单创建成功', 'ORDER', 'CREATE', 2,
 '订单创建成功', '您的订单${orderNo}已创建成功，订单金额：￥${amount}，请及时完成支付。',
 '["orderNo", "amount"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 订单完成
('tpl_order_complete_001', '订单完成通知', 'ORDER', 'COMPLETE', 2,
 '订单已完成', '您的订单${orderNo}已完成，感谢您的使用！如有问题请联系客服。',
 '["orderNo"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 支付成功
('tpl_payment_success_001', '支付成功通知', 'PAYMENT', 'SUCCESS', 2,
 '支付成功', '您的支付已成功！支付金额：￥${amount}，订单号：${orderNo}，感谢您的使用。',
 '["amount", "orderNo"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 系统维护通知
('tpl_system_maintain_001', '系统维护通知', 'SYSTEM', 'MAINTAIN', 1,
 '系统维护通知', '系统将于${startTime}至${endTime}进行维护升级，维护期间服务可能暂时不可用，请您谅解。',
 '["startTime", "endTime"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 用户注册欢迎
('tpl_user_welcome_001', '欢迎注册', 'USER', 'REGISTER', 1,
 '欢迎加入', '欢迎${userName}加入我们！开始您的精彩旅程吧。',
 '["userName"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 评论回复通知
('tpl_comment_reply_001', '评论回复通知', 'COMMENT', 'REPLY', 2,
 '收到新回复', '${replyUserName}回复了您的评论："${content}"',
 '["replyUserName", "content"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 支付失败通知
('tpl_payment_failed_001', '支付失败通知', 'PAYMENT', 'FAILED', 2,
 '支付失败', '很抱歉，您的支付失败了。支付金额：￥${amount}，订单号：${orderNo}，请重新尝试支付。',
 '["amount", "orderNo"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 家庭组解散通知
('tpl_fg_dissolve_001', '家庭组解散通知', 'FAMILY_GROUP', 'DISSOLVE', 2,
 '家庭组解散', '很遗憾，家庭组"${groupName}"已被解散。感谢您的参与！',
 '["groupName"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 订单取消通知
('tpl_order_cancel_001', '订单取消通知', 'ORDER', 'CANCEL', 2,
 '订单已取消', '您的订单${orderNo}已取消，如有疑问请联系客服。',
 '["orderNo"]', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);
