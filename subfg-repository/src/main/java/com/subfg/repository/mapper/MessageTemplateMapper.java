package com.subfg.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.message.MessageTemplatePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息模板 Mapper 接口
 */
@Mapper
public interface MessageTemplateMapper extends BaseMapper<MessageTemplatePo> {

    /**
     * 根据业务类型和场景查询模板
     */
    MessageTemplatePo selectByBusinessAndScene(@Param("businessType") String businessType,
                                             @Param("sceneType") String sceneType);

    /**
     * 查询指定业务类型的所有模板
     */
    List<MessageTemplatePo> selectByBusinessType(@Param("businessType") String businessType);

    /**
     * 查询启用的模板列表
     */
    List<MessageTemplatePo> selectEnabledTemplates();

    /**
     * 根据模板名称查询模板
     */
    MessageTemplatePo selectByTemplateName(@Param("templateName") String templateName);

    /**
     * 查询指定消息类型的模板
     */
    List<MessageTemplatePo> selectByMessageType(@Param("messageType") Integer messageType);
}
