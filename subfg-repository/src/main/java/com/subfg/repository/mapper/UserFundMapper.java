package com.subfg.repository.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.user.UserFundPo;

/**
 * 用户资金 Mapper 接口
 */
@Mapper
public interface UserFundMapper extends BaseMapper<UserFundPo>{

    /**
     * 根据用户ID查询钱包信息
     *
     * @param userId 用户ID
     * @return 钱包信息
     */
    UserFundPo selectByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID更新钱包余额
     *
     * @param userId 用户ID
     * @param userFund 钱包信息
     * @return 影响行数
     */
    int updateByUserId(@Param("userId") String userId, @Param("userFund") UserFundPo userFund);

}
