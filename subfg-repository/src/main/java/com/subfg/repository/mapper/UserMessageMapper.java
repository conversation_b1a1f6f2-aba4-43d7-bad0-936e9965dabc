package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.entity.message.UserMessagePo;

/**
 * 用户消息 Mapper 接口
 */
@Mapper
public interface UserMessageMapper extends BaseMapper<UserMessagePo> {

    /**
     * 查询用户消息列表（支持多种筛选条件）
     */
    IPage<UserMessagePo> selectUserMessages(Page<UserMessagePo> page,
                                          @Param("userId") String userId,
                                          @Param("readStatus") Integer readStatus,
                                          @Param("messageType") Integer messageType,
                                          @Param("businessType") String businessType,
                                          @Param("sceneType") String sceneType,
                                          @Param("priority") Integer priority,
                                          @Param("orderBy") String orderBy);

    /**
     * 统计用户未读消息数量
     */
    Long countUnreadMessages(@Param("userId") String userId);

    /**
     * 统计用户各类型未读消息数量
     */
    Long countUnreadMessagesByType(@Param("userId") String userId, 
                                  @Param("messageType") Integer messageType);

    /**
     * 统计用户各业务类型未读消息数量
     */
    Long countUnreadMessagesByBusiness(@Param("userId") String userId, 
                                     @Param("businessType") String businessType);

    /**
     * 批量标记消息为已读
     */
    int batchMarkAsRead(@Param("messageIds") List<String> messageIds, 
                       @Param("readTime") Long readTime);

    /**
     * 标记用户所有消息为已读
     */
    int markAllAsRead(@Param("userId") String userId, 
                     @Param("readTime") Long readTime);

    /**
     * 根据业务类型和业务ID查询消息
     */
    List<UserMessagePo> selectByBusiness(@Param("businessType") String businessType,
                                       @Param("businessId") String businessId);

    /**
     * 根据业务类型和场景类型查询消息
     */
    List<UserMessagePo> selectByBusinessAndScene(@Param("businessType") String businessType,
                                               @Param("sceneType") String sceneType,
                                               @Param("userId") String userId);
}
