package com.subfg.repository.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.entity.user.UserFundTransactionPo;

/**
 * 用户资金流水 Mapper 接口
 */
@Mapper
public interface UserFundTransactionMapper extends BaseMapper<UserFundTransactionPo> {

    /**
     * 根据用户ID分页查询流水记录
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @return 流水记录分页结果
     */
    Page<UserFundTransactionPo> selectPageByUserId(Page<UserFundTransactionPo> page, @Param("userId") String userId);

    /**
     * 根据用户ID和交易类型查询流水记录
     *
     * @param userId          用户ID
     * @param transactionType 交易类型
     * @return 流水记录列表
     */
    List<UserFundTransactionPo> selectByUserIdAndType(@Param("userId") String userId, 
                                                      @Param("transactionType") Integer transactionType);

    /**
     * 根据用户ID和时间范围查询流水记录
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 流水记录列表
     */
    List<UserFundTransactionPo> selectByUserIdAndTimeRange(@Param("userId") String userId,
                                                           @Param("startTime") Long startTime,
                                                           @Param("endTime") Long endTime);

    /**
     * 根据用户ID统计各类型交易金额
     *
     * @param userId 用户ID
     * @return 统计结果（包含交易类型和总金额）
     */
    List<Object> selectAmountStatsByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询最近的一条成功交易记录
     *
     * @param userId 用户ID
     * @return 最近的成功交易记录
     */
    UserFundTransactionPo selectLatestSuccessTransaction(@Param("userId") String userId);

    /**
     * 根据订单号查询流水记录
     *
     * @param orderNo 订单号
     * @return 流水记录
     */
    UserFundTransactionPo selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据第三方交易号查询流水记录
     *
     * @param thirdTransactionNo 第三方交易号
     * @return 流水记录
     */
    UserFundTransactionPo selectByThirdTransactionNo(@Param("thirdTransactionNo") String thirdTransactionNo);

    /**
     * 统计用户总收入
     *
     * @param userId 用户ID
     * @return 总收入金额
     */
    BigDecimal selectTotalIncomeByUserId(@Param("userId") String userId);

    /**
     * 统计用户总支出
     *
     * @param userId 用户ID
     * @return 总支出金额
     */
    BigDecimal selectTotalExpenseByUserId(@Param("userId") String userId);

    /**
     * 统计用户总提现
     *
     * @param userId 用户ID
     * @return 总提现金额
     */
    BigDecimal selectTotalWithdrawByUserId(@Param("userId") String userId);

    /**
     * 查询处理中的交易记录
     *
     * @param userId 用户ID
     * @return 处理中的交易记录列表
     */
    List<UserFundTransactionPo> selectPendingTransactions(@Param("userId") String userId);

    /**
     * 批量更新交易状态
     *
     * @param transactionIds 交易ID列表
     * @param status         新状态
     * @param completeTime   完成时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("transactionIds") List<String> transactionIds,
                         @Param("status") Integer status,
                         @Param("completeTime") Long completeTime);
}
