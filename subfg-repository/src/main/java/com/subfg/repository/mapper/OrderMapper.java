package com.subfg.repository.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.entity.order.OrderPo;

/**
 * 订单 Mapper 接口
 */
@Mapper
public interface OrderMapper extends BaseMapper<OrderPo> {

    /**
     * 根据用户ID分页查询订单
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @return 订单分页结果
     */
    Page<OrderPo> selectPageByUserId(Page<OrderPo> page, @Param("userId") String userId);

    /**
     * 根据用户ID和订单状态查询订单
     *
     * @param userId      用户ID
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    List<OrderPo> selectByUserIdAndStatus(@Param("userId") String userId, 
                                         @Param("orderStatus") Integer orderStatus);

    /**
     * 根据用户ID和订单类型查询订单
     *
     * @param userId    用户ID
     * @param orderType 订单类型
     * @return 订单列表
     */
    List<OrderPo> selectByUserIdAndType(@Param("userId") String userId, 
                                       @Param("orderType") Integer orderType);

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    OrderPo selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据第三方订单号查询订单
     *
     * @param thirdPartyOrderNo 第三方订单号
     * @return 订单信息
     */
    OrderPo selectByThirdPartyOrderNo(@Param("thirdPartyOrderNo") String thirdPartyOrderNo);

    /**
     * 根据家庭组ID查询订单
     *
     * @param familyGroupId 家庭组ID
     * @return 订单列表
     */
    List<OrderPo> selectByFamilyGroupId(@Param("familyGroupId") String familyGroupId);

    /**
     * 查询过期的待支付订单
     *
     * @param currentTime 当前时间
     * @return 过期订单列表
     */
    List<OrderPo> selectExpiredPendingOrders(@Param("currentTime") Long currentTime);

    /**
     * 统计用户订单数量
     *
     * @param userId 用户ID
     * @return 订单数量统计
     */
    Long countByUserId(@Param("userId") String userId);

    /**
     * 统计用户各状态订单数量
     *
     * @param userId 用户ID
     * @return 状态统计结果
     */
    List<Object> countByUserIdGroupByStatus(@Param("userId") String userId);

    /**
     * 统计用户订单总金额
     *
     * @param userId      用户ID
     * @param orderStatus 订单状态（可选）
     * @return 总金额
     */
    BigDecimal sumAmountByUserId(@Param("userId") String userId, 
                                @Param("orderStatus") Integer orderStatus);

    /**
     * 根据产品ID统计订单数量
     *
     * @param productId 产品ID
     * @return 订单数量
     */
    Long countByProductId(@Param("productId") Integer productId);

    /**
     * 查询用户最近的订单
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 最近订单列表
     */
    List<OrderPo> selectRecentOrdersByUserId(@Param("userId") String userId, 
                                            @Param("limit") Integer limit);

    /**
     * 批量更新订单状态
     *
     * @param orderIds    订单ID列表
     * @param orderStatus 新状态
     * @param updateTime  更新时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("orderIds") List<String> orderIds,
                         @Param("orderStatus") Integer orderStatus,
                         @Param("updateTime") Long updateTime);

    /**
     * 更新订单支付信息
     *
     * @param orderId                   订单ID
     * @param orderStatus               订单状态
     * @param paidAmount               实付金额
     * @param paymentMethod            支付方式
     * @param thirdPartyTransactionNo  第三方交易号
     * @param paidTime                 支付时间
     * @param updateTime               更新时间
     * @return 影响行数
     */
    int updatePaymentInfo(@Param("orderId") String orderId,
                         @Param("orderStatus") Integer orderStatus,
                         @Param("paidAmount") BigDecimal paidAmount,
                         @Param("paymentMethod") Integer paymentMethod,
                         @Param("thirdPartyTransactionNo") String thirdPartyTransactionNo,
                         @Param("paidTime") Long paidTime,
                         @Param("updateTime") Long updateTime);

    /**
     * 根据时间范围查询订单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 订单列表
     */
    List<OrderPo> selectByTimeRange(@Param("startTime") Long startTime, 
                                   @Param("endTime") Long endTime);
}
