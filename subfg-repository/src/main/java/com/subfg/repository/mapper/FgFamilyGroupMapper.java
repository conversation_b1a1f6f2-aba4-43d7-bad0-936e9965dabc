package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.vo.LeaderFamilyGroupVo;
import com.subfg.domain.vo.UserFamilyGroupDetailVo;

/**
 * 家庭组统一 Mapper 接口
 * 支持自建家庭组和拼团家庭组
 */
@Mapper
public interface FgFamilyGroupMapper extends BaseMapper<FgFamilyGroupPo> {

    /**
     * 根据家庭组类型和状态查询家庭组列表
     *
     * @param groupType 家庭组类型（1-自建，2-拼团）
     * @param status    家庭组状态
     * @return 家庭组列表
     */
    List<FgFamilyGroupPo> selectByTypeAndStatus(@Param("groupType") Integer groupType, 
                                              @Param("status") Integer status);

    /**
     * 根据家庭组状态查询家庭组列表
     *
     * @param status 家庭组状态
     * @return 家庭组列表
     */
    List<FgFamilyGroupPo> selectByStatus(@Param("status") Integer status);

    /**
     * 根据团长ID查询家庭组列表
     *
     * @param groupLeaderId 团长用户ID
     * @param groupType     家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FgFamilyGroupPo> selectByGroupLeaderId(@Param("groupLeaderId") String groupLeaderId,
                                              @Param("groupType") Integer groupType);

    /**
     * 根据创建用户ID查询家庭组列表
     *
     * @param createUserId 创建用户ID
     * @param groupType    家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FgFamilyGroupPo> selectByCreateUserId(@Param("createUserId") String createUserId,
                                             @Param("groupType") Integer groupType);

    /**
     * 根据产品ID查询家庭组列表
     *
     * @param productId 产品ID
     * @param groupType 家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FgFamilyGroupPo> selectByProductId(@Param("productId") Integer productId,
                                          @Param("groupType") Integer groupType);

    /**
     * 根据地区ID查询家庭组列表
     *
     * @param regionId  地区ID
     * @param groupType 家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FgFamilyGroupPo> selectByRegionId(@Param("regionId") Integer regionId,
                                         @Param("groupType") Integer groupType);

    /**
     * 查询可加入的家庭组（状态为组建中且未满员）
     *
     * @param productId 产品ID
     * @param regionId  地区ID
     * @param groupType 家庭组类型
     * @return 可加入的家庭组列表
     */
    List<FgFamilyGroupPo> selectAvailableGroups(@Param("productId") Integer productId,
                                              @Param("regionId") Integer regionId,
                                              @Param("groupType") Integer groupType);

    /**
     * 更新家庭组成员数量
     *
     * @param familyGroupId        家庭组ID
     * @param currentMemberCount   当前成员数
     * @param latestJoinTime       最新加入时间（自建家庭组专用）
     * @return 影响行数
     */
    int updateMemberCount(@Param("familyGroupId") String familyGroupId,
                         @Param("currentMemberCount") Integer currentMemberCount,
                         @Param("latestJoinTime") Long latestJoinTime);

    /**
     * 更新家庭组状态
     *
     * @param familyGroupId 家庭组ID
     * @param status        新状态
     * @param updateTime    更新时间
     * @return 影响行数
     */
    int updateStatus(@Param("familyGroupId") String familyGroupId,
                    @Param("status") Integer status,
                    @Param("updateTime") Long updateTime);

    /**
     * 统计用户创建的家庭组数量
     *
     * @param createUserId 创建用户ID
     * @param status       状态（可为空，为空时统计所有状态）
     * @param groupType    家庭组类型（可为空）
     * @return 家庭组数量
     */
    Integer countByCreateUserId(@Param("createUserId") String createUserId,
                               @Param("status") Integer status,
                               @Param("groupType") Integer groupType);

    /**
     * 统计用户担任团长的家庭组数量
     *
     * @param groupLeaderId 团长用户ID
     * @param status        状态（可为空，为空时统计所有状态）
     * @param groupType     家庭组类型（可为空）
     * @return 家庭组数量
     */
    Integer countByGroupLeaderId(@Param("groupLeaderId") String groupLeaderId,
                                @Param("status") Integer status,
                                @Param("groupType") Integer groupType);

    /**
     * 查询即将到期的家庭组
     *
     * @param expireTime 到期时间阈值
     * @param groupType  家庭组类型（可为空）
     * @return 即将到期的家庭组列表
     */
    List<FgFamilyGroupPo> selectExpiringSoon(@Param("expireTime") Long expireTime,
                                           @Param("groupType") Integer groupType);

    /**
     * 查询即将截止的拼团家庭组
     *
     * @param deadline 截止时间阈值
     * @return 即将截止的拼团家庭组列表
     */
    List<FgFamilyGroupPo> selectGroupBuyingNearDeadline(@Param("deadline") Long deadline);

    /**
     * 批量更新家庭组状态
     *
     * @param familyGroupIds 家庭组ID列表
     * @param status         新状态
     * @param updateTime     更新时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("familyGroupIds") List<String> familyGroupIds,
                         @Param("status") Integer status,
                         @Param("updateTime") Long updateTime);

    /**
     * 更新拼团家庭组发车时间
     *
     * @param familyGroupId 家庭组ID
     * @param launchTime    发车时间
     * @param status        新状态
     * @param updateTime    更新时间
     * @return 影响行数
     */
    int updateLaunchTime(@Param("familyGroupId") String familyGroupId,
                        @Param("launchTime") Long launchTime,
                        @Param("status") Integer status,
                        @Param("updateTime") Long updateTime);

    /**
     * 随机获取可加入的家庭组列表
     *
     * @param status 家庭组状态
     * @param limit  返回数量限制
     * @return 随机家庭组列表
     */
    @Select("SELECT * FROM fg_family_group WHERE family_group_status = #{status} AND current_member_count < sum_vacancy ORDER BY RAND() LIMIT #{limit}")
    List<FgFamilyGroupPo> selectRandomAvailableGroups(@Param("status") Integer status,
                                                    @Param("limit") Integer limit);

    /**
     * 联表查询家庭组详情（包含成员信息）
     * 使用 XML 映射实现联表查询
     *
     * @param familyGroupId 家庭组ID
     * @return 家庭组详情（包含成员列表）
     */
    UserFamilyGroupDetailVo selectFamilyGroupDetailById(@Param("familyGroupId") String familyGroupId);

    /**
     * 查询最新通过审核的家庭组列表
     * 按审核时间倒序排序
     *
     * @param limit 返回数量限制
     * @return 最新通过审核的家庭组列表
     */
    List<FgFamilyGroupPo> selectLatestReviewedGroups(@Param("limit") Integer limit);

    /**
     * 分页查询团长管理的家庭组列表（包含成员激活状态统计）
     *
     * @param groupLeaderId 团长用户ID
     * @param offset 偏移量
     * @param limit 每页大小
     * @return 团长管理的家庭组列表（包含成员激活状态统计）
     */
    List<LeaderFamilyGroupVo> selectLeaderGroupsWithMemberStats(@Param("groupLeaderId") String groupLeaderId,
                                                               @Param("offset") Long offset,
                                                               @Param("limit") Long limit);

    /**
     * 统计团长管理的家庭组总数
     *
     * @param groupLeaderId 团长用户ID
     * @return 家庭组总数
     */
    Long countLeaderGroups(@Param("groupLeaderId") String groupLeaderId);

    /**
     * 原子递减家庭组成员数量
     * 使用数据库原子操作确保并发安全
     *
     * @param familyGroupId 家庭组ID
     * @param updateTime    更新时间
     * @return 影响行数（0表示成员数已为0或家庭组不存在）
     */
    @Update("UPDATE fg_family_group SET current_member_count = current_member_count - 1, update_time = #{updateTime} WHERE family_group_id = #{familyGroupId} AND current_member_count > 0")
    int decrementMemberCount(@Param("familyGroupId") String familyGroupId, @Param("updateTime") Long updateTime);

    /**
     * 原子递增家庭组成员数量
     * 使用数据库原子操作确保并发安全
     *
     * @param familyGroupId 家庭组ID
     * @param updateTime    更新时间
     * @return 影响行数
     */
    @Update("UPDATE fg_family_group SET current_member_count = current_member_count + 1, update_time = #{updateTime} WHERE family_group_id = #{familyGroupId}")
    int incrementMemberCount(@Param("familyGroupId") String familyGroupId, @Param("updateTime") Long updateTime);

    /**
     * 原子递增家庭组成员数量并更新最新加入时间（自建家庭组专用）
     * 使用数据库原子操作确保并发安全
     *
     * @param familyGroupId   家庭组ID
     * @param latestJoinTime  最新加入时间
     * @param updateTime      更新时间
     * @return 影响行数
     */
    @Update("UPDATE fg_family_group SET current_member_count = current_member_count + 1, latest_join_time = #{latestJoinTime}, update_time = #{updateTime} WHERE family_group_id = #{familyGroupId}")
    int incrementMemberCountWithJoinTime(@Param("familyGroupId") String familyGroupId,
                                        @Param("latestJoinTime") Long latestJoinTime,
                                        @Param("updateTime") Long updateTime);
}
