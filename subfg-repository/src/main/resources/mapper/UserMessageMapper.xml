<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.subfg.repository.mapper.UserMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.subfg.domain.entity.message.UserMessagePo">
        <id column="message_id" property="messageId" />
        <result column="user_id" property="userId" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="message_type" property="messageType" />
        <result column="read_status" property="readStatus" />
        <result column="read_time" property="readTime" />
        <result column="business_type" property="businessType" />
        <result column="scene_type" property="sceneType" />
        <result column="business_id" property="businessId" />
        <result column="extra_data" property="extraData" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"  />
        <result column="priority" property="priority" />
        <result column="send_time" property="sendTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 查询用户未读消息 -->
    <select id="selectUnreadMessages" resultMap="BaseResultMap">
        SELECT * FROM user_message 
        WHERE user_id = #{userId} 
          AND read_status = 0 
        ORDER BY priority DESC, send_time DESC
    </select>

    <!-- 查询用户消息列表 -->
    <select id="selectUserMessages" resultMap="BaseResultMap">
        SELECT
            message_id,
            user_id,
            title,
            content,
            message_type,
            read_status,
            read_time,
            business_type,
            scene_type,
            business_id,
            extra_data,
            priority,
            send_time,
            create_time,
            update_time
        FROM user_message
        WHERE user_id = #{userId}
        <if test="readStatus != null">
            AND read_status = #{readStatus}
        </if>
        <if test="messageType != null">
            AND message_type = #{messageType}
        </if>
        <if test="businessType != null and businessType != ''">
            AND business_type = #{businessType}
        </if>
        <if test="sceneType != null and sceneType != ''">
            AND scene_type = #{sceneType}
        </if>
        <if test="priority != null">
            AND priority = #{priority}
        </if>
        ORDER BY
        <choose>
            <when test="orderBy != null and orderBy == 'sendTime'">
                send_time DESC
            </when>
            <when test="orderBy != null and orderBy == 'priority'">
                priority DESC, send_time DESC
            </when>
            <when test="orderBy != null and orderBy == 'readStatus'">
                read_status ASC, send_time DESC
            </when>
            <otherwise>
                priority DESC, send_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 统计未读消息数量 -->
    <select id="countUnreadMessages" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user_message 
        WHERE user_id = #{userId} 
          AND read_status = 0
    </select>

    <!-- 统计指定类型未读消息数量 -->
    <select id="countUnreadMessagesByType" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user_message 
        WHERE user_id = #{userId} 
          AND read_status = 0 
          AND message_type = #{messageType}
    </select>

    <!-- 统计指定业务类型未读消息数量 -->
    <select id="countUnreadMessagesByBusiness" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user_message 
        WHERE user_id = #{userId} 
          AND read_status = 0 
          AND business_type = #{businessType}
    </select>

    <!-- 批量标记为已读 -->
    <update id="batchMarkAsRead">
        UPDATE user_message 
        SET read_status = 1, read_time = #{readTime}, update_time = #{readTime}
        WHERE message_id IN 
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

    <!-- 标记所有消息为已读 -->
    <update id="markAllAsRead">
        UPDATE user_message 
        SET read_status = 1, read_time = #{readTime}, update_time = #{readTime}
        WHERE user_id = #{userId} AND read_status = 0
    </update>

    <!-- 根据业务查询消息 -->
    <select id="selectByBusiness" resultMap="BaseResultMap">
        SELECT * FROM user_message 
        WHERE business_type = #{businessType} 
          AND business_id = #{businessId}
        ORDER BY send_time DESC
    </select>

    <!-- 根据业务类型和场景类型查询消息 -->
    <select id="selectByBusinessAndScene" resultMap="BaseResultMap">
        SELECT * FROM user_message 
        WHERE business_type = #{businessType} 
          AND scene_type = #{sceneType}
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        ORDER BY send_time DESC
    </select>

</mapper>
