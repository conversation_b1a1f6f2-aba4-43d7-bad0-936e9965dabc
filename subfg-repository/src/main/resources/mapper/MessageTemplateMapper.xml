<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.subfg.repository.mapper.MessageTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.subfg.domain.entity.message.MessageTemplatePo">
        <id column="template_id" property="templateId" />
        <result column="template_name" property="templateName" />
        <result column="business_type" property="businessType" />
        <result column="scene_type" property="sceneType" />
        <result column="message_type" property="messageType" />
        <result column="title_template" property="titleTemplate" />
        <result column="content_template" property="contentTemplate" />
        <result column="enable" property="enable" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据业务类型和场景查询模板 -->
    <select id="selectByBusinessAndScene" resultMap="BaseResultMap">
        SELECT * FROM message_template 
        WHERE business_type = #{businessType} 
          AND scene_type = #{sceneType}
          AND enable = 1
        LIMIT 1
    </select>

    <!-- 查询指定业务类型的所有模板 -->
    <select id="selectByBusinessType" resultMap="BaseResultMap">
        SELECT * FROM message_template 
        WHERE business_type = #{businessType}
          AND enable = 1
        ORDER BY create_time DESC
    </select>

    <!-- 查询启用的模板列表 -->
    <select id="selectEnabledTemplates" resultMap="BaseResultMap">
        SELECT * FROM message_template 
        WHERE enable = 1
        ORDER BY business_type, scene_type
    </select>

    <!-- 根据模板名称查询模板 -->
    <select id="selectByTemplateName" resultMap="BaseResultMap">
        SELECT * FROM message_template 
        WHERE template_name = #{templateName}
          AND enable = 1
        LIMIT 1
    </select>

    <!-- 查询指定消息类型的模板 -->
    <select id="selectByMessageType" resultMap="BaseResultMap">
        SELECT * FROM message_template 
        WHERE message_type = #{messageType}
          AND enable = 1
        ORDER BY business_type, scene_type
    </select>

</mapper>
