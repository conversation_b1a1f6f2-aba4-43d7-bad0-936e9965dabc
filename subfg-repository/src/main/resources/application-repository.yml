spring:
  datasource:
    # 数据源类型，使用Druid连接池
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************
    username: root
    password: zhyzhy356789

    # Druid连接池配置
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 用来检测连接是否有效的sql，要求是一个查询语句，常用select 'x'
      validation-query: SELECT 1 FROM DUAL
      # 建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      # password:
      database: 0
      timeout: 5000ms
      # 连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 2

    # 集群配置（如果使用集群模式）
    # cluster:
    #   nodes:
    #     - 127.0.0.1:7001
    #     - 127.0.0.1:7002
    #     - 127.0.0.1:7003
    #   max-redirects: 3
    # 哨兵配置（如果使用哨兵模式）
    # sentinel:
    #   master: mymaster
    #   nodes:
    #     - 127.0.0.1:26379
    #     - 127.0.0.1:26380
    #     - 127.0.0.1:26381

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    # 连接超时时间
    connection-timeout: 15000
    # 发布确认
    publisher-confirm-type: correlated
    # 发布返回
    publisher-returns: true
    # 消费者配置
    listener:
      simple:
        # 手动确认消息
        acknowledge-mode: manual
        # 并发消费者数量
        concurrency: 1
        # 最大并发消费者数量
        max-concurrency: 10
        # 预取数量
        prefetch: 1
        # 重试配置
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 1.0

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启 SQL 日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # Mapper XML 文件位置
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体类别名包路径
  type-aliases-package: com.subfg.domain.entity

logging:
  level:
    "[com.subfg.repository.mapper]": DEBUG
